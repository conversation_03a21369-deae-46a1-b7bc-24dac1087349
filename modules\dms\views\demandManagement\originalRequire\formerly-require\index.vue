<template>
    <div class="example-container">
        <demand-list
            ref="demandList"
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :data="treeData"
            :actions-width="170"
            :default-search-open="false"
            :total="total"
            :selectable="checkRowSelectable"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            :enable-lazy-load="true"
            :load-children="loadDemandChildren"
            :table-attrs="{ height: 'calc(100vh - 210px)' }"
            @pagination="query"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @sort-change="handleSortChange"
            @selection-change="handleSelectionChange"
        >
            <template #rightNav>
                <el-button type="text" @click="batchClose" style="margin-right: 10px"> 批量关闭 </el-button>
            </template>
            <template #demandName="{ row }">
                <div class="demand-name-wrapper">
                    <el-tooltip :content="row.demandName" placement="top" :disabled="!row.demandName">
                        <div class="demand-name-container">
                            <svg-icon :icon-class="computedDemandNameIcon(row)" class="svg-icon"></svg-icon>
                            <span class="demand-name-text">{{ row.demandName }}</span>
                            <span class="demand-name-suffix" v-if="row.demandClass !== '产品需求'">
                                <svg-icon
                                    icon-class="dms-demand-tree-numbers"
                                    class="svg-icon"
                                    style="width: 10px"
                                ></svg-icon>
                                {{ row.finishCount || 0 }}/{{ row.allCount || 0 }}
                            </span>
                            <svg-icon
                                v-if="
                                    isPermission && (row.demandClass === '子原始需求' || row.demandClass === '用户需求')
                                "
                                :icon-class="computedplIcon(row)"
                                class="copy-icon"
                                @click="handlebatchCreat(row)"
                            ></svg-icon>
                        </div>
                    </el-tooltip>
                </div>
            </template>
            <!-- 操作列 -->
            <template #actions="{ row }">
                <el-button type="text" @click="getDetail(row)">详情</el-button>
                <el-button
                    type="text"
                    @click="handleEdit(row)"
                    v-if="
                        row.demandClass !== '原始需求' &&
                        row.demandClass !== '子原始需求' &&
                        row.demandStatus !== '已关闭'
                    "
                    >编辑</el-button
                >
                <el-button
                    type="text"
                    @click="handleChange(row)"
                    v-if="
                        row.demandClass !== '原始需求' &&
                        row.demandClass !== '子原始需求' &&
                        row.demandStatus !== '已关闭'
                    "
                    >变更</el-button
                >
            </template>
        </demand-list>
        <OriginalDemand :visible.sync="originalDemandDialogVisible" :demandId="currentDemandId"></OriginalDemand>
        <SubOriginalDemand
            :visible.sync="subOriginalDemandDialogVisible"
            :demandId="currentDemandId"
        ></SubOriginalDemand>
        <ProductDemand :visible.sync="productDemandDialogVisible" :demandId="currentDemandId"></ProductDemand>
        <UserDemand :visible.sync="userDemandDialogVisible" :demandId="currentDemandId"></UserDemand>
        <create-require-pop ref="createRef"></create-require-pop>
        <edit-change-pop ref="editChangeRef"></edit-change-pop>
        <product-pop ref="productRef"></product-pop>
        <!-- 关闭需求弹窗 -->
        <CloseDialog
            :visible.sync="closeDialogVisible"
            :demandId="currentDemandId"
            :rowData="currentRow"
            @success="query"
            @view-detail="handleViewDetailFromClose"
            :isMultiple="isMultiple"
            :rowDataList="selectedRows"
        ></CloseDialog>
    </div>
</template>

<script>
import DemandList from 'dms/views/demandManagement/components/demandList/index.vue';
import { queryParams, queryConfig, navItems } from './formInit.js';
import createRequirePop from '../components/createRequirePop.vue';
import OriginalDemand from 'dms/views/demandManagement/demandDetail/OriginalDemand.vue';
import SubOriginalDemand from 'dms/views/demandManagement/demandDetail/SubOriginalDemand.vue';
import ProductDemand from 'dms/views/demandManagement/demandDetail/ProductDemand.vue';
import UserDemand from 'dms/views/demandManagement/demandDetail/UserDemand.vue';
import CloseDialog from 'dms/views/demandManagement/components/CloseDialog.vue';
import editChangePop from './components/editChangePop.vue';
import productPop from './components/productPop.vue';

export default {
    name: 'FormerlyRequire',
    components: {
        DemandList,
        createRequirePop,
        OriginalDemand,
        SubOriginalDemand,
        ProductDemand,
        UserDemand,
        CloseDialog,
        editChangePop,
        productPop
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '未开始',
            params: { demandStatus: '未开始' },
            // 顶部查询栏配置
            navItems,
            queryParams,
            // 选中的行数据
            selectedRows: [],
            queryConfig,
            // 表格数据
            treeData: [],
            sortOrder: 'descending',
            sortKey: 'proposalStartTime',
            currentPage: 1,
            pageSize: 10,
            total: 100,
            // 原始需求弹窗
            originalDemandDialogVisible: false,
            // 子原始需求弹窗
            subOriginalDemandDialogVisible: false,
            // 产品需求弹窗
            productDemandDialogVisible: false,
            // 用户需求弹窗
            userDemandDialogVisible: false,
            // 当前选中的需求ID
            currentDemandId: '',
            // 当前行数据
            currentRow: {},
            // 关闭需求弹窗
            closeDialogVisible: false,
            // 是否选中多个需求
            isMultiple: false,
            isPermission: null
        };
    },
    watch: {
        'queryParams.productLine': {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getProductOptions(newVal);
                }
            }
        }
    },
    created() {
        this.getProjectOrGroup();
        this.query();
        this.getPermission();
    },
    methods: {
        // 编辑
        handleEdit(row) {
            if (row.demandClass === '用户需求') {
                this.$refs.editChangeRef.open(row, 'edit');
            } else {
                this.$refs.productRef.open(row, 'edit');
            }
        },
        // 变更
        handleChange(row) {
            if (row.demandClass === '用户需求') {
                this.$refs.editChangeRef.open(row, 'change');
            } else {
                this.$refs.productRef.open(row, 'change');
            }
        },
        // 测试需求变更
        handleRequireChange(row) {
            this.$router.push({ name: 'ChangeRequire', query: row });
        },
        // 查询权限
        getPermission() {
            this.$service.dms.original.updatePermission().then((res) => {
                if (res.code === '0000') {
                    this.isPermission = res.data;
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        /**
         * 获取项目或团队
         */
        async getProjectOrGroup() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProjectId').elOptions = res.data.map((i) => ({
                    label: i.projectName,
                    value: i.projectId
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            this.queryParams.assProductId = '';
            try {
                const params = {
                    statusList: ['进行中'],
                    productLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProductId').elOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.ztProductId
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
            }
        },
        /**
         * 查询
         */
        async query() {
            try {
                const params = {
                    ...this.params,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    // 此项必填
                    storyClass: '原始需求',
                    currentPage: this.currentPage,
                    pageSize: this.pageSize
                };
                const api = this.$service.dms.original.getOriginalList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                // 处理数据，为懒加载准备
                this.treeData = this.processTreeData(res.data.list || []);
                this.total = res.data.total || 0;
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        /**
         * 处理树状数据，为懒加载做准备
         * @param {Array} data - 原始数据
         * @returns {Array} 处理后的数据
         */
        processTreeData(data) {
            return data.map((item) => {
                const processedItem = { ...item };
                // 如果有子节点，设置hasChildren为true，并清空children（懒加载时再获取）
                if (item.allCount && item.allCount > 0) {
                    processedItem.hasChildren = true;
                } else {
                    processedItem.hasChildren = false;
                }

                return processedItem;
            });
        },

        /**
         * 懒加载子节点数据
         * @param {Object} tree - 当前节点数据
         * @param {Object} treeNode - 树节点对象
         * @returns {Promise<Array>} 子节点数据
         */
        async loadDemandChildren(tree, treeNode) {
            try {
                const params = {
                    demandId: tree.demandId,
                    storyClass: this.getNextDemandClass(tree.demandClass),
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };

                // 调用API获取子节点数据
                const api = this.$service.dms.demand.getDemandChildren;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return [];
                }
                // 递归处理子节点数据
                return this.processTreeData(res.data || []);
            } catch (error) {
                console.error('加载子节点数据失败:', error);
            }
        },
        /**
         * 获取下一个级别的需求
         * @param {String} currentDemand 当前需求
         * @returns {String} 需求
         */
        getNextDemandClass(currentDemand) {
            if (currentDemand === '原始需求') {
                return '子原始需求';
            } else if (currentDemand === '子原始需求') {
                return '用户需求';
            }
            return '产品需求';
        },
        /**
         * 处理搜索
         * @param {Object} searchData - 搜索表单数据
         */
        handleSearch(searchData) {
            // 调整navTab
            this.activeNavTab = '';
            const { searchParams } = searchData;
            const { proposalTime, expectedDate, costDeviation, actualHour } = searchParams;
            this.params = {
                ...searchParams,
                // 需求提出/创建开始日期
                proposalStartTime: proposalTime[0] || '',
                // 需求提出/创建结束日期
                proposalEndTime: proposalTime[0] || '',
                // 成本起始偏差
                expectedStartDate: expectedDate[0] || '',
                // 成本结束偏差
                expectedEndDate: expectedDate[0] || '',
                // 预估开始工时
                costStartDeviation: costDeviation[0] || '',
                // 预估结束工时
                costEndDeviation: costDeviation[0] || '',
                // 实际开始工时
                actualStartHour: actualHour[0] || '',
                // 实际结束工时
                actualEndHour: actualHour[0] || ''
            };
            this.query();
        },
        /**
         * 处理重置
         */
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
        },
        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            this.params = {
                [queryField]: field
            };
            this.query();
        },
        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },

        /**
         * 跳转至详情页面
         * @param {Object} row - 行数据
         */
        getDetail(row) {
            this.currentDemandId = row.demandId;
            if (row.demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
                return;
            }
            this.productDemandDialogVisible = true;
        },
        /**
         * 选择项变化
         * @param {Array} selections 选中的行数据
         */
        handleSelectionChange(selections) {
            this.selectedRows = selections;
        },
        /**
         * 计算需求名称图标
         * @param {Object} row 行数据
         * @return {String} 图标名称
         */
        computedDemandNameIcon(row) {
            if (row.demandClass === '原始需求') {
                return 'dms-demand-original-demand';
            } else if (row.demandClass === '子原始需求') {
                return 'dms-demand-sub-original-demand';
            } else if (row.demandClass === '用户需求') {
                return 'dms-demand-user-demand';
            }
            return 'dms-demand-product-demand';
        },
        computedIcon(row) {
            if (row.demandClass === '子原始需求' || row.demandClass === '用户需求') {
                return 'cj';
            }
            return '';
        },
        computedplIcon(row) {
            if (row.demandClass === '子原始需求' || row.demandClass === '用户需求') {
                return 'plcj';
            }
            return '';
        },
        // 快捷创建
        handleCreat(row) {
            this.$router.push({
                path: './CreateRequire',
                query: { demandClass: row.demandClass, type: 'kj' }
            });
        },
        // 非快捷创建
        handlebatchCreat(row) {
            this.$refs.createRef.open(row);
        },
        /**
         * 关闭需求
         * @param {Object} row - 行数据
         */
        handleClose(row) {
            this.isMultiple = false;
            this.currentDemandId = row.demandId;
            this.currentRow = row;
            this.closeDialogVisible = true;
        },
        batchClose() {
            if (this.selectedRows.length === 0) {
                this.$message.warning('请先选择需求');
                return;
            }
            this.isMultiple = true;
            this.closeDialogVisible = true;
        },
        /**
         * 从关闭弹窗查看需求详情
         * @param {Object} data - 包含demandId和demandClass的对象
         */
        handleViewDetailFromClose(data) {
            const { demandId, demandClass } = data;
            this.currentDemandId = demandId;
            if (demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
            } else if (demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
            } else if (demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
            } else if (demandClass === '产品需求') {
                this.productDemandDialogVisible = true;
            }
        },
        /**
         * 检查行是否可选择
         * @param {Object} row 行数据
         * @param {Number} index 行索引
         * @return {Boolean} 是否可选择
         */
        checkRowSelectable(row, index) {
            if (row.demandStatus === '已关闭') {
                return false;
            }
            return true;
        },
        showCloseButton(row) {
            if (row.demandStatus === '已关闭') {
                return false;
            }
            return (
                row.demandClass === '产品需求' ||
                row.demandClass === '用户需求' ||
                (row.demandClass === '原始需求' && row.demandStatus !== '待审核' && row.demandStatus !== '已关闭')
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.example-container {
    padding: 0 20px;
}
.svg-icon {
    padding: 0;
}

.demand-name-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 32px; // 确保最小高度，避免换行
}

.demand-name-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;
    flex: 1;

    .demand-name-text {
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 15px;
    }

    .demand-name-suffix {
        font-size: 10px;
        position: absolute;
        right: 0;
        color: #999;
        flex-shrink: 0;
        white-space: nowrap; // 防止数字换行
    }
    .copy-icon {
        width: 20px;
        height: 20px;
        margin-right: 35px;
        cursor: pointer;
    }
}
.create-demand-button {
    margin-right: 20px;
}
.actions-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

::v-deep .el-input-number.change-time-input {
    width: 100%;
}

// 确保树状表格的展开箭头和内容在同一行
::v-deep .el-table__row {
    .el-table__expand-column {
        .el-table__expand-icon {
            display: inline-flex;
            align-items: center;
            vertical-align: middle;
        }
    }
}

// 只对包含需求名称的列应用 flex 布局，避免影响其他列的 showOverflowTooltip
::v-deep td .cell:has(.demand-name-wrapper) {
    display: flex;
    align-items: center;
    line-height: 1.5;
    min-height: 32px;
}
</style>
