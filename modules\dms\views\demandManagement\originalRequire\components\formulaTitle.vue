<!-- 标题组件 -->
<template>
    <div class="title-box">
        <span class="title-line"></span>
        <span class="title-text">{{ title }}</span>
    </div>
</template>

<script>
export default {
    props: {
        title: {
            type: String,
            required: true
        }
    }
};
</script>

<style scoped lang="scss">
.title-box {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
}

.title-line {
    height: 14px;
    padding-left: 8px;
    border-left: 3px solid #3370ff;
}

.title-text {
    font-weight: bolder;
    color: #333;
    padding-left: 3px;
}
</style>
