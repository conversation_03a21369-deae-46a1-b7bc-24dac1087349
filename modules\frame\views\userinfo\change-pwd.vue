<template>
    <div class="view">
        <div class="container">
            <h1>{{ $t('frame.changePwd') }}</h1>
        </div>
    </div>
</template>

<script>
import funcitons from '../../mixins/functions';

export default {
    mixins: [funcitons],
    data() {
        return {
            // 短信发送倒计时
            sendCountDown: 0,
            formDataInfo: {
                newPwd: '',
                confirmPwd: '',
                smsCaptcha: ''
            }
        };
    },
    computed: {
        rules() {
            const rulePwd = [
                {
                    required: true,
                    message: this.$t('frame.changePwd.message.ruleEmptyNewPwd'),
                    trigger: ['blur', 'change']
                },
                {
                    pattern: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、])[\da-zA-Z`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]{8,20}$/,
                    message: this.$t('frame.changePwd.message.ruleNewPwd'),
                    trigger: ['blur', 'change']
                }
            ];
            return {
                newPwd: rulePwd,
                confirmPwd: [
                    ...rulePwd,
                    {
                        validator: (rule, value, callback) => {
                            if (value !== this.formDataInfo.newPwd) {
                                callback(
                                    new Error(
                                        this.$t(
                                            'frame.changePwd.message.ruleEqualPwd'
                                        )
                                    )
                                );
                            } else {
                                callback();
                            }
                        },
                        trigger: ['blur', 'change']
                    }
                ],
                smsCaptcha: [
                    {
                        required: true,
                        message: this.$t(
                            'frame.changePwd.message.ruleEmptySmsCaptcha'
                        ),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[0-9]{6}$/,
                        message: this.$t(
                            'frame.changePwd.message.ruleSmsCaptcha'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
    },
    created() {},
    methods: {
        // 发送短信验证码
        sendSmsCaptcha() {
            // 执行倒计时
            this.sendCountDown = 60;
            const t = setInterval(() => {
                if (this.sendCountDown === 0) {
                    clearInterval(t);
                }
                this.sendCountDown--;
            }, 1000);
            this.$service.frame
                .getSmsVerifCode({ token: '1' })
                .then((response) => {
                    const isSuccess = response.head.code === '000000';
                    if (!isSuccess) {
                        this.$message({
                            message: response.head.message,
                            type: 'error'
                        });
                        clearInterval(t);
                        this.sendCountDown = 0;
                    }
                });
        },
        // 保存接口
        postSave() {
            const data = {
                newPwd: this.$tools.encrypt(this.formDataInfo.newPwd, '3DES'),
                confirmPwd: this.$tools.encrypt(
                    this.formDataInfo.confirmPwd,
                    '3DES'
                ),
                smsCaptcha: this.formDataInfo.smsCaptcha
            };
            this.$service.frame.updatePwd(data).then((response) => {
                const {code} = response.head;
                if (code === '000000') {
                    this.$message({
                        message: this.$t('frame.changePwd.message.saveSuccess'),
                        type: 'success'
                    });
                    // 跳转到登录接口
                    this.loginOutHandler();
                } else {
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        handlePaste() {
            // 禁止密码复制粘贴，所以该函数为空即可
        },
        save() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.postSave();
                } else {
                    this.$message({
                        message: this.$t('frame.changePwd.message.ruleNoPass'),
                        type: 'error'
                    });
                }
            });
        },
        // 登出处理函数
        loginOutHandler() {
            const _this = this;
            // 获取权限按钮
            const params = {
                sysType: '0' // 系统标识 0：web系统 1:移动端 2：终端
            };
            this.$service.frame
                .putlogOut(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.loginOutSuccessHandler(_this);
                    } else {
                        this.loginOutFailedHandler(_this);
                    }
                })
                .catch(() => {
                    this.loginOutFailedHandler(_this);
                });
        },
        // 登出成功处理函数
        loginOutSuccessHandler(_this) {
            _this.$store.dispatch('user/logout');
            _this.$router.push(`/login`);
        },
        // 登出失败处理函数
        loginOutFailedHandler(_this) {
            _this.$message({
                title: _this.$t('common.failed'),
                message: _this.$t(
                    'frame.msg.loginOutFailed'
                ),
                type: 'success',
                duration: 2000
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.container{
    .content{
        width: 650px;
        margin: 25px auto;
        font-weight: 400;
    }
    .line{
        background: #E7E7E7;
        margin-bottom: 25px;
    }
    .password-header{
        height: 35px;
        font-size: 14px;
        font-weight: 500;
        color: #1E222D;
        border-bottom: 1px solid #E7E7E7;
    }
    .el-form-item.is-required.el-form-item--normal{
        margin-top: 26px;
    }
}

.footer {
    text-align: center;
}
// .el-input-group__append button.el-button {
.send-sms-code,
.send-sms-code:hover {
    border-radius: 0px;
    color: #ffffff !important;
    width: 160px;
    font-size: 14px;
    background: linear-gradient(0deg, #3370FF, #6B91F8);
}
.el-input-group__append .send-sms-code--disabled {
    border-radius: 0px;
    width: 160px;
    background-color: #f5f7fa;
    color: #909399;
    border: 1px solid #dcdfe6;
    border-left: 0px;
    border-right: 0px;
}

</style>
