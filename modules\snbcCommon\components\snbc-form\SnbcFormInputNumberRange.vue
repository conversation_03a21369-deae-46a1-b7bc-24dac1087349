<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-input-number
            class="snbc-form-input-number-range"
            v-model="config.modelObj[config.modelKey][0]"
            v-bind="elInputNumberAttrs"
            :style="{ width: inputWidth }"
            :min="leftMin"
            :max="leftMax"
        />
        <span style="padding: 0 5px">至</span>
        <el-input-number
            class="snbc-form-input-number-range"
            v-model="config.modelObj[config.modelKey][1]"
            v-bind="elInputNumberAttrs"
            :style="{ width: inputWidth }"
            :min="rightMin"
            :max="rightMax"
        />
    </el-form-item>
</template>
<script>
import isUndefined from 'lodash/isUndefined';

export default {
    name: 'SnbcFormInputNumberRange',
    props: {
        /**
         * 组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elInputNumberAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-input组件默认属性设置
            defaultElInputNumberAttrs: {
                'clearable': true,
                'controls': false,
                'controls-position': 'right'
            }
        };
    },
    computed: {
        // 第一个数值组件最小值
        leftMin() {
            if (isUndefined(this.elInputNumberAttrs.min)) {
                return this.elInputNumberAttrs.min;
            }
            return -Infinity;
        },
        // 第一个数值组件最大值
        leftMax() {
            const { modelObj, modelKey } = this.config;
            const max = modelObj[modelKey][1];
            if (max !== undefined) {
                return max;
            } else if (isUndefined(this.elInputNumberAttrs.max)) {
                return this.elInputNumberAttrs.max;
            }
            return Infinity;
        },
        // 第二个数值组件最小值
        rightMin() {
            const { modelObj, modelKey } = this.config;
            const min = modelObj[modelKey][0];
            if (min !== undefined) {
                return min;
            } else if (isUndefined(this.elInputNumberAttrs.min)) {
                return this.elInputNumberAttrs.min;
            }
            return -Infinity;
        },
        // 第二个数值组件最大值
        rightMax() {
            if (isUndefined(this.elInputNumberAttrs.max)) {
                return this.elInputNumberAttrs.max;
            }
            return Infinity;
        },
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-input-number组件应用属性
        elInputNumberAttrs() {
            return {
                ...this.defaultElInputNumberAttrs,
                placeholder: `请输入${this.config.name}`,
                ...(this.config.elInputNumberAttrs || {})
            };
        },
        // 输入框宽度
        inputWidth() {
            return this.config.width || 'calc(50% - 13px)';
        }
    },
    methods: {}
};
</script>
