<template>
    <div>
        <el-dialog :title="title" :visible.sync="localVisible" width="60%" class="custom-dialog" @close="cancel">
            <el-form :model="addForm" ref="dataForm" label-width="120px" :rules="userRules">
                <el-form-item label="变更原因" prop="changeReason" v-if="this.type !== 'edit'">
                    <el-input type="textarea" v-model="addForm.changeReason" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <div class="add-flex">
                    <el-form-item label="需求名称" prop="requireName">
                        <el-input
                            v-model="addForm.requireName"
                            placeholder="请输入需求名称"
                            :disabled="true"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="优先级" prop="priority">
                        <el-select v-model="addForm.priority" placeholder="请选择优先级" clearable>
                            <el-option
                                v-for="item in priorityData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="add-flex">
                    <el-form-item label="产品模块" prop="priority" v-if="this.row.demandClass === '产品需求'">
                        <el-select v-model="addForm.priority" placeholder="请选择产品模块" clearable filterable>
                            <el-option
                                v-for="item in priorityData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="期望交付日期" prop="deliveryDate">
                        <el-date-picker
                            v-model="addForm.deliveryDate"
                            type="date"
                            placeholder="请选择期望交付日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="需求等级" prop="deliveryDate">
                        <el-select v-model="addForm.deliveryDate" placeholder="请选择需求等级" clearable>
                            <el-option
                                v-for="item in typeData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label="需求描述" prop="requireDescription">
                    <el-input type="textarea" v-model="addForm.requireDescription" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <el-form-item label="验收标准" prop="expectedReturn">
                    <el-input type="textarea" v-model="addForm.expectedReturn" maxlength="500" :rows="4"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button type="primary">确认</el-button>
                <el-button type="info" @click="cancel">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import Constant from 'dms/constant/dict.js';

const { typeData, priorityData } = Constant;
export default {
    name: 'CreateRequirePop',
    props: {
        title: {
            type: String,
            default() {
                return '';
            }
        }
    },
    data() {
        return {
            // 弹窗信息
            localVisible: false,
            addForm: {},
            // 传参数据
            type: '',
            row: {},
            typeData,
            priorityData,
            userRules: {
                changeReason: [
                    {
                        required: true,
                        message: '请输入变更原因',
                        trigger: 'blur'
                    }
                ],
                requireName: [
                    {
                        required: true,
                        message: '请输入需求名称',
                        trigger: 'change'
                    }
                ],
                priority: [
                    {
                        required: true,
                        message: '请选择优先级',
                        trigger: 'change'
                    }
                ],
                requireDescription: [
                    {
                        required: true,
                        message: '请输入需求描述',
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    methods: {
        // 打开弹窗
        open(row, type) {
            this.row = row;
            this.type = type;
            this.localVisible = true;
        },
        // 关闭弹窗
        cancel() {
            this.$nextTick(() => {
                this.localVisible = false;
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.add-flex {
    display: flex;
    justify-content: space-between;
}
</style>
