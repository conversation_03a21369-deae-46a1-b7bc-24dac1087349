/* eslint-disable max-lines-per-function */
/**
 * 将数据转换为树状结构
 * @param {Object} data 数据
 * @param {Array} department 部门
 * @param {Array} departmentOption 部门选项
 * @param {Boolean} isGroupOrProject 是否在团队或者项目页面，是的话就不显示团队的编辑和删除
 * @returns {Object} 树状结构
 */
export function generateTreeData(data, department, departmentOption, isGroupOrProject) {
    const level = department ? department.length : 0;

    const createTeamChildren = (team, teamNodeId) => {
        const roleOrder = [
            'Java',
            'Android',
            '前端',
            'C/C++',
            'C#',
            '需求',
            '运维',
            '数据库',
            'python',
            '软件测试',
            'UI/UE'
        ];
        // 根据这个顺序将模块排序
        const roles = Object.keys(team.moduleMap).sort((a, b) => {
            const indexA = roleOrder.indexOf(a);
            const indexB = roleOrder.indexOf(b);
            // 如果角色不在预定义顺序中，放到最后
            if (indexA === -1 && indexB === -1) return 0;
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;
            return indexA - indexB;
        });

        if (!team.teamId) {
            const members = roles.length > 0 ? team.moduleMap[roles[0]] : [];
            if (!Array.isArray(members) || !members.length) {
                return [];
            }
            return members.reduceRight((acc, member) => {
                let memberColor = '';
                if (member.multiTeam) {
                    memberColor = '#8b3e86';
                } else if (member.type !== '本部门') {
                    memberColor = '#FF7F00';
                }
                return [
                    {
                        id: member.id,
                        name: member.memberName,
                        title: '工程师',
                        backgroundColor: '#d4f4ff',
                        color: '#3297e4',
                        borderColor: memberColor,
                        iconColor: '#32CD32',
                        showIcon: true,
                        canDelete: !!team.teamId,
                        belongOrgCode: team.teamId,
                        children: acc
                    }
                ];
            }, []);
        }

        const restMembers = roles
            .map((roleName) => {
                const members = team.moduleMap[roleName];
                if (!Array.isArray(members) || !members.length) {
                    return null;
                }
                return {
                    id: `${team.teamId}-${roleName}`,
                    name: `${roleName} (${members.length})`,
                    title: roleName,
                    backgroundColor: '#3370ff',
                    color: '#fff',
                    showIcon: false,
                    children: members.reduceRight((acc, member) => {
                        let memberColorObject = {};
                        if (member.multiTeam && member.type && member.type !== '本部门') {
                            memberColorObject = {
                                topHalfBorderColor: '#8b3e86',
                                bottomHalfBorderColor: '#FF7F00'
                            };
                        } else if (member.multiTeam) {
                            memberColorObject = { borderColor: '#8b3e86' };
                        } else if (member.type && member.type !== '本部门') {
                            memberColorObject = { borderColor: '#FF7F00' };
                        }
                        return [
                            {
                                id: member.id,
                                name: member.memberName,
                                title: '工程师',
                                backgroundColor: '#d4f4ff',
                                color: '#3297e4',
                                iconColor: '#32CD32',
                                showIcon: true,
                                canDelete: !!team.teamId,
                                belongOrgCode: team.teamId,
                                children: acc,
                                ...memberColorObject
                            }
                        ];
                    }, [])
                };
            })
            .filter(Boolean);
        let colorObject = {};
        // 判断项目经理是不是来自于多团队
        if (team.multiTeam) {
            colorObject = { borderColor: '#8b3e86' };
        }

        // 判断项目经理是不是来自于多部门
        if (team.type && team.type !== '本部门') {
            colorObject = { borderColor: '#FF7F00' };
        }
        // 多部门多团队的情况
        if (team.multiTeam && team.type && team.type !== '本部门') {
            colorObject = {
                topHalfBorderColor: '#8b3e86',
                bottomHalfBorderColor: '#FF7F00'
            };
        }
        return [
            {
                id: team.teamId,
                name: team.teamLeader,
                teamName: team.teamLeader,
                teamLeaderAccount: team.teamLeaderAccount,
                title: '项目经理',
                backgroundColor: '#d4f4ff',
                color: '#3297e4',
                canAdd: false,
                canDelete: false,
                canEdit: false,
                showIcon: true,
                // 所属部门code
                children: restMembers,
                ...colorObject
            }
        ];
    };

    const createTeamNode = (team, orgCode, collapsed = true) => {
        return {
            id: team.teamId,
            name: `${team.teamName}(${team.internalCount}${
                team.externalCount === 0 ? '' : ` + ${team.externalCount}`
            })`,
            teamName: team.teamName,
            teamLeaderAccount: team.teamLeaderAccount,
            title: '项目经理',
            backgroundColor: '#3370ff',
            color: '#fff',
            canAdd: !!team.teamId,
            canDelete: !isGroupOrProject && !!team.teamId && !(level === 3),
            canEdit: !isGroupOrProject && !!team.teamId,
            width: team.teamId ? 260 : 160,
            showIcon: false,
            addBtnColor: isGroupOrProject ? '#fff' : '#000',
            editBtnColor: isGroupOrProject ? '#fff' : '#000',
            collapsed,
            // 所属部门code
            belongOrgCode: orgCode,
            children: createTeamChildren(team, team.teamId)
        };
    };

    const createOrgNode = (org) => ({
        id: level === 1 ? org.orgCode : departmentOption[1].orgCode,
        name: `${level === 1 ? org.orgName : departmentOption[1].orgName}(${org.orgMemberNo})`,
        title: '二级部门',
        backgroundColor: '#3370ff',
        color: '#fff',
        addBtnColor: '#fff',
        canAdd: true,
        showIcon: false,
        collapsed: level === 1,
        children: org.teamList.map((team) => createTeamNode(team, org.orgCode))
    });

    if (level <= 1) {
        const numbers = data.reduce((acc, cur) => acc + cur.orgMemberNo, 0);
        return {
            id: departmentOption[0].orgCode,
            name: `${departmentOption[0].orgName}(${numbers})`,
            title: '一级部门',
            backgroundColor: '#3370ff',
            color: '#fff',
            showIcon: false,
            children: data.map(createOrgNode)
        };
    }

    if (level === 2) {
        if (!data || data.length === 0) return {};
        return createOrgNode(data[0]);
    }

    if (level === 3) {
        if (!data || data.length === 0 || !data[0].teamList || data[0].teamList.length === 0) return {};
        const org = data[0];
        const team = org.teamList[0];
        return createTeamNode(team, org.orgName, false);
    }

    // 默认返回空对象
    return {};
}
