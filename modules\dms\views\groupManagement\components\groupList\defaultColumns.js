export const defaultColumns = [
    {
        type: 'selection',
        width: 55,
        label: '选择框',
        columnManage: {
            sortableDisabled: true,
            // 固定在第一行
            pinnedFirst: true,
            widthDisabled: true
        }
    },
    {
        prop: 'teamId',
        label: '团队ID',
        width: 140,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'teamName',
        label: '团队名称',
        width: 300,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'teamLeader',
        label: '团队负责人',
        width: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'teamType',
        label: '团队类型',
        width: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'departmentName',
        label: '所属部门',
        width: 200,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'memberCount',
        label: '成员数量',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'internalCount',
        label: '内部成员',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'externalCount',
        label: '外部成员',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'status',
        label: '状态',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'createTime',
        label: '创建时间',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'updateTime',
        label: '更新时间',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    }
];
