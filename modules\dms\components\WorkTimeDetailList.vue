<template>
    <div>
        <div class="flex column-direction">
            <div
                v-if="handleProjectMerge(index)"
                class="font-weight-800 taskList ml-6"
                :class="handleProjectMerge(index) ? 'red-point' : ''"
            >
                项目：{{ item.proProjectName }}
            </div>
            <div class="flex">
                <div class="taskList">
                    <div class="task-detail">
                        <span style="color: #3370ff">任务：</span
                        >{{ item.taskName }}
                    </div>
                    <div class="task-detail">
                        计划：{{ item.startDate }} -
                        {{ item.endDate }}
                    </div>
                </div>
                <div class="taskTime">
                    {{ item.taskHours }}
                </div>
            </div>
        </div>
        <div class="flex" v-if="item.effortInfoList && view === 'day'">
            <div class="task-title">日志：</div>
            <div class="flex column-direction">
                <div
                    v-for="(
                        { effortVal, effortHours }, effortInfoListIndex
                    ) in item.effortInfoList"
                    class="work-hour-detail-list"
                    :key="effortInfoListIndex"
                >
                    <span
                        v-if="
                            item.effortInfoList &&
                            item.effortInfoList.length > 1
                        "
                        >{{ CIRCLED_NUMBERS[effortInfoListIndex] }}</span
                    >
                    <span class="ellipsis">
                        {{ effortVal }}
                    </span>
                    <span class="work-hour-dialog">{{ effortHours }}h</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
/**
 * 该组件用于点击工时时展示的工时详情列表
 */
import { CONSTANTS } from '@/constants.js';

export default {
    name: 'WorkTimeDetailList',
    props: {
        index: {
            type: Number,
            default: 0
        },
        item: {
            type: Object,
            default() {
                return {};
            }
        },
        // 日视图/月视图
        view: {
            type: String,
            default: 'day'
        },
        taskDetail: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            CIRCLED_NUMBERS: CONSTANTS.CIRCLED_NUMBERS
        };
    },
    methods: {
        /**
         * 处理相同的项目合并，与上一行相同的项目就不显示
         * @param {Number} index 序号
         * @returns {Boolean} 项目名称是否与上一行相同
         */
        handleProjectMerge(index) {
            if (this.taskDetail.length === 0) return false;
            if (index === 0) return true;
            return (
                this.taskDetail[index].proProjectName !==
                this.taskDetail[index - 1].proProjectName
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.column-direction {
    flex-direction: column;
}
.font-weight-800 {
    font-weight: 800;
}
.ml-6 {
    margin-left: 6px;
}
.work-hour-detail-list {
    width: 280px;
}
.info {
    justify-content: space-between;
    margin-bottom: 20px;
}
.total-cost {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    .total-cost-numbers {
        font-size: 20px;
        text-decoration: underline;
    }
}
.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
.taskList {
    width: 385px;
    padding-left: 20px;
    position: relative;
    margin-top: 5px;
}
.red-point::before {
    content: '●';
    color: #ec808d;
    font-size: 20px;
    position: absolute;
    left: 0;
    top: -2px;
    line-height: 1;
}
.taskTime {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 40px;
    background-color: #ffff80;
    font-weight: 800;
    font-size: 16px;
    margin: 5px 0 5px 10px;
    line-height: 50px;
}
.task-detail {
    margin: 2px 0 0 5px;
}
.work-hour-dialog {
    color: #409eff;
    width: 40px;
    white-space: nowrap;
    margin-left: auto;
}
.task-title {
    margin-left: 25px;
    width: fit-content;
}
</style>
