/**
 * 项目管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 项目管理接口
        project: {
            // 获取项目详情
            getProjectDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/selectProjectInfo',
                    method: 'get',
                    params: data
                });
            },
            // 获取项目列表
            getProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/selectProjectList',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
