import './icons'; // icon

import moduleRoutes from './router';
import moduleI18n from './lang/index.js';
import moduleStore from './store';
import moduleService from './service';
import config from './config.js';

const moduleName = config.moduleName;

export default ({ Vue, router, store, i18n }) => {
    // 加载国际化
    Vue.prototype.$addI18n(moduleName, moduleI18n);
    // 注册路由
    Vue.prototype.$addRoutes(moduleName, moduleRoutes);
    // 注册状态树
    Vue.prototype.$addStore(moduleName, moduleStore);
    // 注册模块service
    Vue.prototype.$service[moduleName] = moduleService(Vue);
    // 注册静态变量
    // Vue.prototype.$addConstant(moduleName, moduleConstant);
    console.log(
        `%c${moduleName}模块加载完成`,
        'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
    );
};
