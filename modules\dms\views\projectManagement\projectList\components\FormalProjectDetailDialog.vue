<template>
    <div>
        <el-dialog title="项目详情" :visible.sync="dialogVisible" width="85%" top="5vh">
            <div class="project-detail">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>
                <el-descriptions :column="3" class="detail-descriptions">
                    <el-descriptions-item label="项目名称">
                        {{ projectData.projectName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目经理">
                        {{ projectData.projectManagerName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="PPQA">
                        {{ projectData.ppqaName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="开始时间">
                        {{ projectData.startTime || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="计划结束时间">
                        {{ projectData.plannedEndTime || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="实际结束时间">
                        {{ projectData.actualEndTime || '' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 关联信息 -->
                <div class="title">关联信息</div>
                <el-descriptions :column="3" class="detail-descriptions">
                    <el-descriptions-item label="项目归属"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="所属部门">
                        {{ projectData.departmentName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="产品线">
                        {{ projectData.productLineName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="产品经理">
                        {{ projectData.productManager || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="涉及产品">
                        {{ projectData.involvedProductName || '' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 其他信息 -->
                <div class="title">其他信息</div>
                <el-descriptions :column="3" class="detail-descriptions">
                    <el-descriptions-item label="项目来源"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="项目类型"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="项目立项类型"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="项目规模"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="项目难度等级"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="公司立项等级"> {{}} </el-descriptions-item>
                    <el-descriptions-item label="项目级别"> {{}} </el-descriptions-item>
                </el-descriptions>

                <!-- 公司立项项目信息 -->
                <div class="title">公司立项项目信息</div>
                <el-descriptions :column="3" class="detail-descriptions">
                    <el-descriptions-item label="公司立项项目名称">
                        {{ projectData.companyProjectName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="立项决议函名称">
                        {{ projectData.approvalDocumentName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="立项时间">
                        {{ projectData.approvalTime || '' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 硬件项目团队信息 -->
                <div class="title">硬件项目团队信息</div>
                <el-descriptions :column="2" class="detail-descriptions">
                    <el-descriptions-item label="硬件产品经理">
                        {{ projectData.hardwareProductManager || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="硬件项目经理">
                        {{ projectData.hardwareProjectManager || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="硬件团队代表" :span="2">
                        {{ projectData.hardwareTeamRepresentative || '' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 项目描述 -->
                <div class="title">项目描述</div>
                <el-descriptions :column="1" class="detail-descriptions">
                    <el-descriptions-item label="任务来源">
                        <div class="text-content">{{ projectData.taskSource || '' }}</div>
                    </el-descriptions-item>
                    <el-descriptions-item label="任务描述">
                        <div class="text-content">{{ projectData.taskDescription || '' }}</div>
                    </el-descriptions-item>
                    <el-descriptions-item label="任务分析">
                        <div class="text-content">{{ projectData.taskAnalysis || '' }}</div>
                    </el-descriptions-item>
                    <el-descriptions-item label="总体要求">
                        <div class="text-content">{{ projectData.overallRequirements || '' }}</div>
                    </el-descriptions-item>
                    <el-descriptions-item label="技术要求">
                        <div class="text-content">{{ projectData.technicalRequirements || '' }}</div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">关闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'FormalProjectDetailDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectData: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            // 选项映射
            projectOwnershipOptions: {
                internal: '内部项目',
                external: '外部项目',
                cooperation: '合作项目'
            },
            projectSourceOptions: {
                customer: '客户需求',
                market: '市场调研',
                innovation: '技术创新',
                strategy: '战略规划'
            },
            projectTypeOptions: {
                product: '产品开发',
                technology: '技术研发',
                integration: '系统集成',
                service: '服务项目'
            },
            projectApprovalTypeOptions: {
                company: '公司立项',
                department: '部门立项',
                temporary: '临时立项'
            },
            projectScaleOptions: {
                small: '小型',
                medium: '中型',
                large: '大型',
                xlarge: '超大型'
            },
            projectDifficultyLevelOptions: {
                low: '低',
                medium: '中',
                high: '高',
                extreme: '极高'
            },
            companyApprovalLevelOptions: {
                A: 'A级',
                B: 'B级',
                C: 'C级',
                D: 'D级'
            },
            projectLevelOptions: {
                level1: '一级项目',
                level2: '二级项目',
                level3: '三级项目'
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.project-detail {
    .title {
        @include section-title;
        margin-bottom: 16px;
        margin-top: 32px;

        &:first-child {
            margin-top: 0;
        }
    }

    .detail-descriptions {
        margin-bottom: 20px;
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 20px;
        color: #303133;
        max-height: 200px;
        overflow-y: auto;
    }
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 13px;
    }
}
</style>
