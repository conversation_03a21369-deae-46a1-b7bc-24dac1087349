// 统一风格样式修改
#app .sidebar-container .el-submenu .el-menu-item,
#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title {
    min-width: 200px !important;
}
.header:not(:first-child) {
    margin-top: 0;
}
.hasTagsView .fixed-header + .app-main {
    padding-top: 95px !important;
}
.hasTagsView .app-main {
    height: calc(100vh - 95px) !important;
}
// 去除脚注
.fixed-footer {
    display: none;
}
// 去除左上角logo
.sidebar-logo.svg-icon {
    display: none;
}
.fixed-header {
    width: calc(100% - 200px) !important;
    .navbar {
        height: 50px;
    }
}
.hideSidebar .fixed-header,
.hideSidebar .fixed-footer {
    width: calc(100% - 54px) !important;
}
#app .sidebar-container {
    width: 200px !important;
    .sidebar-logo-container {
        height: 50px !important;
        line-height: 50px !important;
        .sidebar-title {
            font-size: 15px !important;
        }
    }
}
.main-container {
    margin-left: 200px !important;
}
#app .hideSidebar .main-container {
    margin-left: 54px !important;
}

.el-submenu__title {
    height: 38px;
    line-height: 38px;
    font-size: 12px;
}

.el-menu-item {
    height: 38px !important;
    line-height: 38px !important;
    font-size: 12px !important;
}

.view {
    padding: 5px 10px 50px 10px;
    background-color: #fff;
    height: calc(100vh - 95px);
}

.margin-top-10 {
    margin-top: 10px !important;
}
.margin-bottom-10 {
    margin-bottom: 10px !important;
}
.padding-right-0 {
    padding-right: 0 !important;
}
.padding-top-5 {
    padding-top: 5px !important;
}

.el-table {
    .el-table__row.warning-row {
        background: #e6a23c;
    }

    .el-table__row.success-row {
        background: #67c23a;
    }

    .el-table__row.danger-row {
        background: #f56c6c;
    }

    .el-table__row.info-row {
        background: #409eff;
    }
}

.el-dialog__header {
    display: flex;
    align-items: center;
    padding: 18px 20px;
    height: 50px;
    background: #f5f6fa;

    .el-dialog__title {
        font-size: 14px;
        font-weight: 700;
        color: #000;

        &::before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 15px;
            background: #3370ff;
            margin-right: 10px;
            position: relative;
            top: 2px;
        }
    }

    .el-dialog__close {
        font-size: 15px;
        color: #000;
        font-weight: 700;
    }
}
.el-dialog {
    .el-dialog__body {
        padding: 20px;
    }
}
.query-area-form {
    .el-form-item__label {
        display: inline-flex;
        height: 40px;
        align-items: center;
        line-height: 16px;
        justify-content: right;
    }
    .el-input-group__prepend {
        line-height: 38px;
    }
}

.query-label-line2 {
    .query-area-form {
        .el-form-item__label {
            display: inline-flex;
            height: 32px;
            align-items: center;
            line-height: 16px;
            justify-content: right;
        }
    }
}

.content {
    .assets-title {
        padding: 8px 0;
        font-size: 16px;
    }
}
.snbc-card {
    .snbc-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.snbc-text-primary {
    color: #3370ff;
}
.snbc-text-success {
    color: #13ce66;
}
.snbc-text-warning {
    color: #ffba00;
}
.snbc-text-danger {
    color: #ff4949;
}

.question-tip {
    font-size: 14px;
    cursor: help;
}

.snbc-header.header {
    display: flex;
    margin: 0;
    flex-direction: column;
    .header-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        .header__title {
            flex: 1;
            display: flex !important;
            align-items: center;
            &::before {
                display: inline-block;
                content: '';
                height: 14px;
                padding-left: 8px;
                border-left: 3px solid #3370ff;
            }
        }
    }
}

// 全局table样式
.dms-table.el-table .el-table__row {
    height: auto !important;
}
.dms-table.el-table th {
    background-color: #3370ff !important;
    border-bottom: 1px solid #8c8c8c !important;
    border-right: 1px solid #8c8c8c !important;
}
.dms-table.el-table th > .cell {
    color: #fff !important;
}
.dms-table.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
// 修改排序箭头样式
.dms-table.el-table .ascending .sort-caret.ascending {
    border-bottom-color: #ffdc37;
}
.dms-table.el-table .descending .sort-caret.descending {
    border-top-color: #ffdc37;
}

.el-table__header {
    border-bottom: 1px solid #dfe6ec !important;
}

.el-table th.el-table__cell.is-leaf,
.el-table td.el-table__cell {
    height: 50px !important;
}

th {
    background: rgb(245, 246, 250) !important;
}
// 隐藏功能导航下拉菜单
.navbar .left-menu .el-dropdown {
    display: none !important;
}
.view-box {
    width: 100%;
    padding: 10px;
    height: calc(100vh - 105px);
    overflow: auto;
    display: flex;
    flex-direction: column;
}
.sprint-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
}
