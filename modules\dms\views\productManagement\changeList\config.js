import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, monthRange } = CommonItems;

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine',
    component: 'SnbcFormProductSelect'
};

const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};

const productCode = {
    ...input,
    name: '产品编号',
    modelKey: 'productCode'
};

const productOwner = {
    ...input,
    name: 'Product Owner',
    modelKey: 'productOwner'
};

const productId = {
    ...input,
    name: '产品ID',
    modelKey: 'productId'
};

const checkUser = {
    ...input,
    name: '审批人',
    modelKey: 'checkUser'
};

const applyDate = {
    ...monthRange,
    name: '申请日期',
    modelKey: 'applyDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

const checkDate = {
    ...monthRange,
    name: '审批日期',
    modelKey: 'checkDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [productLine, productName, productCode, productOwner, productId, checkUser, applyDate, checkDate]
};

// 查询条件参数
export const queryParams = {
    productLine: '',
    productName: '',
    productCode: '',
    productOwner: '',
    productId: '',
    checkUser: '',
    applyDate: [],
    checkDate: []
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'productStatus' },
    { field: 'active', name: '待审核', queryField: 'productStatus' },
    { field: 'paused', name: '已通过', queryField: 'productStatus' },
    { field: 'closed', name: '已拒绝', queryField: 'productStatus' },
    { field: 'closed', name: '已撤回', queryField: 'productStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productId',
        label: 'ID',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productCode',
        label: '产品编号',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '产品名称',
        width: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwner',
        label: 'Product Owner',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkUser',
        label: '审批人',
        width: 160
    },
    {
        prop: 'applyDate',
        label: '申请日期',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'checkResult',
        label: '审批结果',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkDate',
        label: '审批日期',
        width: 160
    }
];
