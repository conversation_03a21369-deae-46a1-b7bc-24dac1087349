<template>
    <div>
        <!-- 按钮 -->
        <div class="add-btn">
            <el-button type="info" @click="handleBack()">返回</el-button>
        </div>
        <!-- 页签菜单 -->
        <el-menu default-active="0" class="" mode="horizontal" @select="handleTabSelect">
            <el-menu-item v-for="(tab, index) in tabsPageConfig.tabItems" :index="index.toString()" :key="tab">{{
                tab
            }}</el-menu-item>
        </el-menu>
        <!-- 动态组件区域 -->
        <component :is="currentComponent" :id="id"></component>
    </div>
</template>
<script>
import baseInfo from './baseInfo/index.vue';
import productStructure from './productStructure/index.vue';
import connectionInfo from './connectionInfo/index.vue';

export default {
    name: 'AddProduct',
    components: {
        baseInfo,
        productStructure,
        connectionInfo
    },
    data() {
        return {
            currentComponent: 'baseInfo',
            tabsPageConfig: {
                activeName: '基本信息',
                tabItems: ['基本信息', '产品结构', '干系人']
            },
            id: ''
        };
    },
    created() {
        if (this.$route?.query?.id) {
            this.id = this.$route.query.id;
        }
    },
    methods: {
        handleTabSelect(index) {
            const components = ['baseInfo', 'productStructure', 'connectionInfo'];
            this.currentComponent = components[parseInt(index)];
        },
        // 返回上一页
        handleBack() {
            this.$router.back();
        }
    }
};
</script>
<style lang="scss" scoped>
.add-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
::v-deep .el-menu.el-menu--horizontal {
    display: flex;
    justify-content: flex-end;
    margin: 1vh 0;
    border-bottom: 3px solid #3370ff;
    .el-menu-item {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
        color: #4377ee;
        height: 45px;
        line-height: 45px;
    }
    .el-menu-item.is-active {
        background-color: #4377ee;
        color: #fff;
    }
}
</style>
