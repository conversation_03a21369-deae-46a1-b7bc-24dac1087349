<template>
    <div>
        <DepartmentSelector :key="projectSelectorKey"></DepartmentSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="工时负载" name="resourceLoad" :lazy="true">
                    <ResourceLoad class="mt-15" :activeName="activeName"></ResourceLoad>
                </el-tab-pane>
                <el-tab-pane label="组织成员" name="departmentManagementOrgChart">
                    <ProjectOrgChart
                        @updateTopList="updateTopList"
                        :activeName="activeName"
                        :showOrgChart="showOrgChart"
                    ></ProjectOrgChart>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import ProjectOrgChart from 'dms/components/projectOrgChart/index.vue';
import ResourceLoad from './resourceLoad/index.vue';
import DepartmentSelector from 'dms/components/TopSelectorGroup/DepartmentSelector.vue';

export default {
    name: 'Resources',
    components: {
        ProjectOrgChart,
        DepartmentSelector,
        ResourceLoad
    },
    data() {
        return {
            activeName: 'resourceLoad',
            // 顶部级联组件key
            projectSelectorKey: 0,
            department: ['软件开发部']
        };
    },
    computed: {
        showOrgChart() {
            if (!this.$store.state.dms?.departmentOption) return false;
            return !!this.$store.state.dms?.departmentOption[0] || false;
        }
    },
    activated() {
        // 因为做了缓存，其他页面改变项目之后本页面的级联选择器不会同步，改变key令其同步
        this.projectSelectorKey += 1;
    },
    beforeDestroy() {
        this.$store.dispatch('dms/setDepartment', []);
        this.$store.dispatch('dms/setDepartmentOption', []);
    },
    methods: {
        updateTopList() {
            this.projectSelectorKey += 1;
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 10px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.mt-15 {
    margin-top: 15px;
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
.resourceLoad {
    margin-top: 20px;
}
</style>
