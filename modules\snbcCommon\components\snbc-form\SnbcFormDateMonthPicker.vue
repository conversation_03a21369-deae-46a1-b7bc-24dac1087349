<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-date-picker
            v-model="config.modelObj[config.modelKey]"
            v-bind="elDatePickerAttrs"
            :value-format="valueFormat"
        />
    </el-form-item>
</template>

<script>
export default {
    name: 'SnbcFormDateMonthPicker',
    props: {
        /**
         * SnbcFormDateMonthPicker
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elDatePickerAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-date-picker组件默认属性设置
            defaultElDatePickerAttrs: {
                type: 'monthrange',
                placeholder: '请选择年月区间',
                valueFormat: 'yyyy-MM',
                format: 'yyyy-MM',
                rangeSeparator: '至',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期'
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-date-picker组件应用属性
        elDatePickerAttrs() {
            return {
                ...this.defaultElDatePickerAttrs,
                ...(this.config.elDatePickerAttrs || {})
            };
        },
        // 直接返回默认的 valueFormat
        valueFormat() {
            return this.defaultElDatePickerAttrs.valueFormat;
        }
    }
};
</script>
