<template>
    <div class="work-time-bar">
        <el-popover
            popper-class="resources-workTimeBar--popper"
            placement="bottom"
            width="320"
            trigger="click"
            :disabled="this.workHour === '0.0' || this.workHour === '0' || taskList.taskShowVoList === null || isLast"
            ref="popover"
            :popper-options="{ boundariesElement: 'viewport' }"
        >
            <div class="popoverContent">
                <el-skeleton :rows="4" animated :loading="loading" :throttle="1500">
                    <template>
                        <div class="taskContainer" v-for="(item, index) in taskDetail" :key="index">
                            <WorkTimeDetailList
                                :index="index"
                                :item="item"
                                :taskDetail="taskDetail"
                                :view="view"
                            ></WorkTimeDetailList>
                        </div>
                    </template>
                </el-skeleton>
            </div>
            <div
                v-if="this.workHour !== -1 && this.workHour !== '0.0' && this.workHour !== '0' && !isLast"
                slot="reference"
                :style="{
                    'height': '40px',
                    'min-width': view === 'day' ? '30px' : '60px'
                }"
                class="specialHour"
                :class="abnormalWorkHoursClass"
                :title="computedTooltip"
            >
                <!-- 本项目上已消耗工时 -->
                <span
                    @click="handleClick('project-hours')"
                    class="consumed-project-hours"
                    v-if="isValidProjectConsumedHour"
                    >{{ taskList.currentProjectHours }}</span
                >
                <!-- 本项目上预计工时 -->
                <span @click="handleClick('project-hours')" class="plan-project-hours" v-if="isValidProjectPlanHour">{{
                    taskList.currentProjectHours
                }}</span>
                <!-- 个人预计总工时 -->
                <span
                    @click="handleClick('whole-hours')"
                    class="whole-hours"
                    :style="{
                        'height': isValidProjectPlanHour ? '20px' : '40px',
                        'line-height': isValidProjectPlanHour ? '20px' : '40px',
                        'border-radius': isValidProjectPlanHour ? '0 0 5px 5px' : '5px'
                    }"
                    v-if="isValidWholePlanHour"
                    >{{ taskList.planHours }}</span
                >
            </div>
            <div
                slot="reference"
                v-show="(this.workHour === '0.0' || this.workHour === '0') && !isLast"
                :style="{
                    'min-width': view === 'day' ? '60px' : '60px',
                    'color': '#333'
                }"
                class="specialHour"
                :class="abnormalWorkHoursClass"
                :title="computedTooltip"
            >
                {{ this.taskList.workStatus === '正常' ? '-' : '' }}
            </div>
            <div
                slot="reference"
                v-show="isLast && this.workHour !== -1"
                :style="{
                    'min-width': view === 'day' ? '60px' : '60px'
                }"
                class="specialHour"
            >
                {{ workHour === '0.0' || workHour === '0' ? '-' : workHour }}
            </div>
            <div
                slot="reference"
                v-show="workHour === -1"
                :class="abnormalWorkHoursClass === 'on-business' ? 'on-business' : ''"
                :title="computedTooltip"
            ></div>
        </el-popover>
    </div>
</template>
<script>
import { CONSTANTS } from '@/constants.js';
import WorkTimeDetailList from 'dms/components/WorkTimeDetailList.vue';

export default {
    name: 'WorkTimeBar',
    components: {
        WorkTimeDetailList
    },
    props: {
        view: { type: String, default: 'day' },
        taskList: { type: Object, default: () => ({ taskShowVoList: [] }) },
        isLast: { type: Boolean, default: false },
        isPlanHour: { type: Boolean, default: false }
    },
    data() {
        return {
            CIRCLED_NUMBERS: CONSTANTS.CIRCLED_NUMBERS
        };
    },
    computed: {
        // 分配的工作时长可以为0，如果为null，则不显示
        workHour() {
            if (this.isPlanHour) {
                return this.taskList.planHours ?? -1;
            }
            return this.taskList.currentProjectHours ?? -1;
        },
        // 任务详情
        taskDetail() {
            return this.taskList.taskShowVoList ?? [];
        },
        loading() {
            return this.taskDetail.length === 0;
        },
        // 动态计算高度
        buttonHeight() {
            if (this.workHour === -1) {
                return 0;
            }
            return 30;
        },
        // 是否是有效的项目已消耗工时
        isValidProjectConsumedHour() {
            return (
                !this.isPlanHour &&
                this.taskList.currentProjectHours !== '0' &&
                this.taskList.currentProjectHours !== '0.0'
            );
        },
        // 是否是有效的项目预计工时
        isValidProjectPlanHour() {
            return (
                this.isPlanHour &&
                this.taskList.currentProjectHours !== '0' &&
                this.taskList.currentProjectHours !== '0.0'
            );
        },
        // 是否是有效的总预计工时
        isValidWholePlanHour() {
            return this.isPlanHour && this.taskList.planHours !== '0.0' && this.taskList.planHours !== '0';
        },
        // 异常工时对应的样式(只有日视图生效)
        abnormalWorkHoursClass() {
            if (this.view === 'month') {
                return '';
            }
            const map = {
                正常: '',
                请假: 'on-leave',
                出差: 'on-business'
            };
            return map[this.taskList.workStatus || '正常'];
        },
        // 休假与出差的提示语
        computedTooltip() {
            const map = {
                '': '',
                'on-leave': '请假',
                'on-business': '出差'
            };
            return map[this.abnormalWorkHoursClass];
        }
    },
    watch: {
        taskDetail(newVal) {
            if (newVal.length !== 0) {
                this.$nextTick(() => {
                    this.$refs.popover.updatePopper();
                });
            }
        }
    },
    methods: {
        handleClick(type) {
            this.$emit('click', { isNeedProjectId: type === 'project-hours' });
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.work-time-bar {
    height: 40px;
    display: flex;
    .el-button {
        font-weight: bold;
        color: #fff;
        padding: 0px;
        min-width: 20px;
        font-size: 10px;
        text-align: center;
        margin-top: auto;
    }
    span {
        display: inline-block !important;
    }
}
.specialHour {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    font-weight: 600;
}

.consumed-project-hours {
    width: 100%;
    height: 100%;
    background-color: #999;
    color: #fff;
    border-radius: 5px;
    line-height: 40px;
    cursor: pointer;
}
.plan-project-hours {
    width: 100%;
    background-color: #85b67a;
    color: #fff;
    border-radius: 5px 5px 0 0;
    line-height: 20px;
    cursor: pointer;
}
.whole-hours {
    width: 100%;
    background-color: #4a9ef7;
    color: #fff;
    cursor: pointer;
}

.work-hour-dialog {
    color: #409eff;
    width: 40px;
    white-space: nowrap;
    margin-left: auto;
}
.task-title {
    margin-left: 25px;
    width: fit-content;
}
@mixin work-status($status) {
    &::after {
        content: $status;
        position: absolute;
        top: 1px;
        right: 1px;
        line-height: 1;
        color: #022777;
        background-color: rgba(255, 255, 0, 0.5);
        padding: 1px;
        border-radius: 50rem;
        font-weight: 500;
        transition: transform 0.2s ease;
        cursor: pointer;
    }

    &:hover::after {
        transform: scale(1.5);
    }
    &::after::after {
        content: attr(title);
    }
}
.on-leave {
    @include work-status('假');
    font-size: 10px;
    @include respond-to('xl') {
        font-size: 9px;
    }
}
.on-business {
    @include work-status('差');
    font-size: 10px;
    @include respond-to('xl') {
        font-size: 9px;
    }
}
</style>
<style lang="scss">
// 弹窗样式，与app同级，只能写在这里
.el-popper.el-popover.resources-workTimeBar--popper {
    max-height: 360px;
    overflow: auto;
    width: 420px !important;
}
</style>
