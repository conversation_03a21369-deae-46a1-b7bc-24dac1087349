<template>
    <div>
        <el-dialog title="编辑子需求" :visible.sync="dialogVisible" width="600px" top="15vh">
            <el-form :model="form" :rules="rules" ref="form" label-width="120px" label-position="left">
                <el-form-item label="需求名称" prop="storyName" required>
                    <el-input v-model="form.storyName"></el-input>
                </el-form-item>

                <el-form-item label="预估工时" prop="estimate" required class="hours-item">
                    <div class="flex">
                        <el-input-number
                            v-model="form.estimate"
                            :min="0"
                            :controls="false"
                            style="width: 150px; margin-right: 20px"
                        >
                        </el-input-number>
                        <div class="unit">d</div>
                    </div>
                </el-form-item>

                <el-form-item label="期望交付日期" prop="deliveryDate" required class="date-item">
                    <el-date-picker
                        v-model="form.deliveryDate"
                        type="date"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 150px"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'UserDemand',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        demandId: {
            type: String,
            default: ''
        },
        rowData: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            form: {
                storyName: '',
                estimate: null,
                deliveryDate: ''
            },
            rules: {
                storyName: [{ required: true, message: '请输入需求名称', trigger: ['blur', 'change'] }],
                estimate: [{ required: true, message: '请输入预估工时', trigger: ['blur', 'change'] }],
                deliveryDate: [{ required: true, message: '请选择期望交付日期', trigger: ['blur', 'change'] }]
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                const { demandName, estimateHour, expectedDate } = this.rowData;
                this.form = {
                    storyName: demandName,
                    estimate: estimateHour,
                    deliveryDate: expectedDate,
                    id: this.demandId
                };
            }
        }
    },
    methods: {
        /**
         * 确认提交
         */
        handleConfirm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.save();
                } else {
                    return false;
                }
            });
        },
        async save() {
            const params = this.form;
            const api = this.$service.dms.demand.editSubDemand;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$emit('success');
                this.$message.success('保存成功');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$refs.form && this.$refs.form.resetFields();
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';
.flex {
    display: flex;
}
.form-row {
    display: flex;
    gap: 40px;

    .hours-item {
        margin-right: 20px;
    }

    .date-item {
        flex: 1;
        margin-right: 0;
    }
}

.dialog-footer {
    text-align: center;

    .el-button {
        min-width: 80px;
    }
}

::v-deep .el-form-item {
    margin-bottom: 20px;
}

::v-deep .el-form-item__label {
    color: #303133;
    font-weight: 800;
}
</style>
