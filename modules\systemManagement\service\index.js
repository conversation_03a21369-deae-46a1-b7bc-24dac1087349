/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    let service = {
        /**
         * 获取左侧菜单列表
         */
        getMenuList(data) {
            return http({
                baseDomain: basePath.systemApi.system,
                url: '/authority/getPermissionTree',
                method: 'get',
                params: data
            });
        }
    };

    // 合并modules中的服务
    const fileService = require.context('./modules', false, /\.js/);
    fileService.keys().forEach((moduleFilePath) => {
        const moduleName = moduleFilePath.replace(/^\.\/(.*)\/.\w+$/, '$1');
        const tempService = fileService(moduleName);
        service = Object.assign(service, tempService.default(Vue));
    });

    return service;
};
