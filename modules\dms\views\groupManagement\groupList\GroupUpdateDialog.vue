<template>
    <div>
        <el-dialog title="创建团队" :visible.sync="dialogVisible" width="85%" top="5vh">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="团队名称" prop="teamName">
                            <el-input v-model="form.teamName" placeholder="请输入团队名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="Team Leader" prop="teamLeader">
                            <PeopleSelector v-model="form.teamLeader" placeholder="请选择Team Leader"> </PeopleSelector>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="团队类型" prop="teamType">
                            <el-select v-model="form.teamType" placeholder="请选择">
                                <el-option
                                    v-for="item in teamTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 关联信息 -->
                <div class="title">关联信息</div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="直属上级" prop="directSuperior">
                            <PeopleSelector v-model="form.directSuperior" placeholder="请选择请选择直属上级">
                            </PeopleSelector>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属部门" prop="department">
                            <el-select v-model="form.department" placeholder="请选择">
                                <el-option
                                    v-for="item in departmentOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="产品线" prop="productLine">
                            <el-select v-model="form.productLine" placeholder="请选择">
                                <el-option
                                    v-for="item in productLineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="负责产品" prop="responsibleProduct">
                            <el-select v-model="form.responsibleProduct" placeholder="请选择">
                                <el-option
                                    v-for="item in productOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 团队愿景 -->
                <div class="title">团队愿景</div>
                <el-form-item prop="teamVision">
                    <el-input
                        v-model="form.teamVision"
                        type="textarea"
                        :rows="4"
                        placeholder="建议参考的模板：在<时间范围>内成为<领域>的<角色定位>，通过<核心手段>实现<变革性影响>"
                    >
                    </el-input>
                </el-form-item>

                <!-- 业务和职能范围 -->
                <div class="title">业务和职能范围</div>
                <el-form-item prop="businessScope">
                    <el-input
                        v-model="form.businessScope"
                        type="textarea"
                        :rows="4"
                        placeholder="建议参考的模板：核心业务边界---<直接负责领域>；协同边界---与<关联团队>共同承担的<交叉职能>"
                    >
                    </el-input>
                </el-form-item>

                <!-- 质量技术目标及要求 -->
                <div class="title">质量技术目标及要求</div>
                <el-form-item prop="qualityTechGoals">
                    <el-input
                        v-model="form.qualityTechGoals"
                        type="textarea"
                        :rows="4"
                        placeholder="建议参考的模板：基础线---100%满足<行业标准/合规要求>；挑战线---<关键质量维度>达到<行业标杆>水平；技术雷达---主攻<核心技术方向>，限制使用<淘汰技术>，试点<前沿技术>"
                    >
                    </el-input>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    name: 'CreateGroupDialog',
    components: { PeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            form: {
                teamName: '',
                teamLeader: '',
                teamType: '',
                directSuperior: '',
                department: '',
                productLine: '',
                responsibleProduct: '',
                teamVision: '',
                businessScope: '',
                qualityTechGoals: ''
            },
            rules: {
                teamName: [{ required: true, message: '请输入团队名称', trigger: 'blur' }],
                teamLeader: [{ required: true, message: '请选择Team Leader', trigger: 'blur' }],
                department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
                productLine: [{ required: true, message: '请选择产品线', trigger: 'change' }],
                responsibleProduct: [{ required: true, message: '请选择负责产品', trigger: 'change' }]
            },
            // 团队类型选项
            teamTypeOptions: [
                { label: '开发团队', value: 'development' },
                { label: '测试团队', value: 'testing' },
                { label: '产品团队', value: 'product' },
                { label: '运维团队', value: 'operations' }
            ],
            // 部门选项
            departmentOptions: [],
            // 产品线选项
            productLineOptions: [],
            // 产品选项
            productOptions: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initData();
            }
        }
    },
    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            this.resetForm();
            // await this.getDepartmentOptions();
            // await this.getProductLineOptions();
            // await this.getProductOptions();
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = {
                teamName: '',
                teamLeader: '',
                teamType: '',
                directSuperior: '',
                department: '',
                productLine: '',
                responsibleProduct: '',
                teamVision: '',
                businessScope: '',
                qualityTechGoals: ''
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.clearValidate();
            });
        },
        /**
         * 获取部门选项
         */
        async getDepartmentOptions() {
            try {
                const res = await this.$service.dms.common.getOrgList({
                    orgCode: '0002'
                });
                if (res.code === '0000') {
                    this.departmentOptions = this.formatDepartmentOptions(res.data);
                }
            } catch (error) {
                console.error('获取部门列表失败:', error);
            }
        },
        /**
         * 格式化部门选项
         */
        formatDepartmentOptions(data) {
            const options = [];
            data.forEach((dept) => {
                if (dept.children && dept.children.length > 0) {
                    dept.children.forEach((subDept) => {
                        options.push({
                            label: subDept.orgName,
                            value: subDept.orgCode
                        });
                    });
                }
            });
            return options;
        },
        /**
         * 获取产品线选项
         */
        async getProductLineOptions() {
            try {
                const res = await this.$service.dms.common.getProductLineList();
                if (res.code === '0000') {
                    this.productLineOptions = res.data.map((item) => ({
                        label: item.productLineName,
                        value: item.productLineId
                    }));
                }
            } catch (error) {
                console.error('获取产品线列表失败:', error);
            }
        },
        /**
         * 获取产品选项
         */
        async getProductOptions() {
            try {
                const res = await this.$service.dms.common.getProductList();
                if (res.code === '0000') {
                    this.productOptions = res.data.map((item) => ({
                        label: item.productName,
                        value: item.productId
                    }));
                }
            } catch (error) {
                console.error('获取产品列表失败:', error);
            }
        },
        /**
         * 提交表单
         */
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    try {
                        const res = await this.$service.dms.group.addGroupMembersAndGroupLeader({
                            teamName: this.form.teamName,
                            teamLeaderAccount: this.form.teamLeader,
                            teamType: this.form.teamType,
                            directSuperior: this.form.directSuperior,
                            departmentCode: this.form.department,
                            productLine: this.form.productLine,
                            responsibleProduct: this.form.responsibleProduct,
                            teamVision: this.form.teamVision,
                            businessScope: this.form.businessScope,
                            qualityTechGoals: this.form.qualityTechGoals
                        });
                        if (res.code === '0000') {
                            this.$message.success('创建团队成功');
                            this.closeDialog();
                            this.$emit('success');
                        } else {
                            this.$message.error(res.message || '创建团队失败');
                        }
                    } catch (error) {
                        console.error('创建团队失败:', error);
                        this.$message.error('创建团队失败');
                    }
                }
            });
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep .form .el-form-item__label {
    font-weight: bold;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
</style>
