import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict.js';

const { select, input, monthRange } = CommonItems;

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine',
    component: 'SnbcFormProductSelect'
};

const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};

const productCode = {
    ...input,
    name: '产品编号',
    modelKey: 'productCode'
};

const productOwner = {
    ...input,
    name: 'Product Owner',
    modelKey: 'productOwner'
};

const productId = {
    ...input,
    name: '产品ID',
    modelKey: 'productId'
};

const status = {
    ...select,
    name: '产品状态',
    modelKey: 'status',
    elOptions: dictData.statusData
};

const createDate = {
    ...monthRange,
    name: '创建日期',
    modelKey: 'createDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

const closeDate = {
    ...monthRange,
    name: '关闭日期',
    modelKey: 'closeDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [productLine, productName, productCode, productOwner, productId, status, createDate, closeDate]
};

// 查询条件参数
export const queryParams = {
    productLine: '',
    productName: '',
    productCode: '',
    productOwner: '',
    productId: '',
    status: '',
    foundDate: [],
    closeDate: []
};

// 导航栏配置
export const navItems = [
    { field: '', name: '全部产品', queryField: 'productStatus' },
    { field: 'active', name: '进行中', queryField: 'productStatus' },
    { field: 'paused', name: '已暂停', queryField: 'productStatus' },
    { field: 'closed', name: '已关闭', queryField: 'productStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productId',
        label: 'ID',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productCode',
        label: '产品编号',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '产品名称',
        width: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwnerName',
        label: 'Product Owner',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'createDate',
        label: '创建日期',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'productStatus',
        label: '产品状态',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'project',
        label: '负责团队/项目',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'closedDate',
        label: '关闭日期',
        width: 160
    }
];
