<!-- 操作记录组件 -->
<template>
    <div>
        <el-collapse>
            <el-collapse-item v-for="(item, index) in collapseItems" :key="index" :name="item.name">
                <!-- 自定义标题插槽 -->
                <template #title>
                    <div class="collapse-title">
                        <div class="approval-note">{{ item.date }} 由{{ item.operator }} {{ item.status }}流程</div>
                        <div class="approval-note">审批意见：{{ item.opinion }}</div>
                    </div>
                </template>
                <div>{{ item.concept1 }}</div>
                <div>{{ item.concept2 }}</div>
                <div>{{ item.concept3 }}</div>
                <div>{{ item.concept4 }}</div>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script>
export default {
    props: {
        collapseItems: {
            type: Array,
            required: true
        }
    }
};
</script>

<style scoped lang="scss">
.collapse-title {
    display: flex;
    flex-direction: column;
    height: 100% !important;
}
::v-deep .el-collapse-item__header {
    height: 80px !important;
}
.approval-note {
    height: 50%;
    display: flex;
    align-items: center;
    color: #333;
}
</style>
