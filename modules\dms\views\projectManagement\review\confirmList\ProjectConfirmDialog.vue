<template>
    <div>
        <el-dialog title="项目确认" :visible.sync="dialogVisible" width="550px">
            <div class="confirm-container">
                <div class="description">请确认将该项目作为一个新项目导入，或与一个临时项目合并：</div>
                <el-select v-model="importType" placeholder="">
                    <el-option v-for="item in importOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select class="project-select" v-if="importType === '合并项目'" v-model="projectId" placeholder="">
                    <el-option v-for="item in projectOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <div class="detail-button">
                    <el-button type="text"> 查看项目详情 </el-button>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="handleConfirm">确 认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'FormalProjectDetailDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectData: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            // 导入类型
            importType: '导入项目',
            // 项目ID
            projectId: '',
            importOptions: [
                { label: '导入项目', value: '导入项目' },
                { label: '合并项目', value: '合并项目' }
            ],
            projectOptions: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    methods: {
        async handleConfirm() {
            if (this.importType === '合并项目' && !this.projectId) {
                this.$message.warning('请选择项目');
            }
            // TODO: 调接口
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.project-detail {
    .title {
        @include section-title;
        margin-bottom: 16px;
        margin-top: 32px;

        &:first-child {
            margin-top: 0;
        }
    }
}
.confirm-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .description {
        margin-bottom: 22px;
    }
    .project-select {
        margin-top: 12px;
        width: 400px;
    }
    // TODO:加长
    .detail-button {
        margin-top: 12px;
    }
}
</style>
