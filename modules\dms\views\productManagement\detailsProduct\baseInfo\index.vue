<template>
    <div class="view-box">
        <formula-title :title="title"></formula-title>
        <el-descriptions :column="2">
            <el-descriptions-item label="产品ID">{{ productInfo.id || '-' }} </el-descriptions-item>
            <el-descriptions-item label="产品名称">{{ productInfo.productName || '-' }} </el-descriptions-item>
            <el-descriptions-item label="产品编号">{{ productInfo.productCode || '-' }} </el-descriptions-item>
            <el-descriptions-item label="产品线">{{ productInfo.productLine || '-' }}</el-descriptions-item>
            <el-descriptions-item label="Product Owner">{{ productInfo.productOwnerName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建日期">{{ productInfo.createDate || '-' }}</el-descriptions-item>
            <el-descriptions-item label="关闭日期">{{ productInfo.closedDate || '-' }}</el-descriptions-item>
            <el-descriptions-item label="产品状态">{{ productInfo.productStatus || '-' }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions :column="1">
            <el-descriptions-item label="目标用户">{{ productInfo.targetUsers || '-' }}</el-descriptions-item>
            <el-descriptions-item label="产品定位">{{ productInfo.productPosition || '-' }}</el-descriptions-item>
            <el-descriptions-item label="核心价值">{{ productInfo.coreValue || '-' }}</el-descriptions-item>
            <el-descriptions-item label="竞争策略">{{ productInfo.competitiveStrategy || '-' }}</el-descriptions-item>
        </el-descriptions>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';

export default {
    components: { formulaTitle },
    props: {
        id: {
            type: String,
            default: () => ''
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            title: '产品详情',
            productInfo: {}
        };
    },
    created() {
        // 当组件创建时，如果 id 有值则查询产品信息
        if (this.id) {
            this.getProductInfo();
        }
    },
    methods: {
        async getProductInfo() {
            try {
                const params = {
                    productId: this.id
                };
                const res = await this.$service.dms.product.getProductInfo(params);
                if (res.code === '0000') {
                    this.productInfo = res.data;
                } else {
                    this.$message.error(res.message || '获取产品详情失败');
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 13px;
    }
}
</style>
