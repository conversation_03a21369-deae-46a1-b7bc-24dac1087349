require('../hbs-extra');

const fs = require('fs');
const path = require('path');
const { notEmpty } = require('../utils.js');

/**
 * 获取目录下所以子目录名称
 * @param {string} dir 目录路径
 */
function getDirs(dir) {
    // 当前模块所在目录路径
    const dirPath = path.resolve(dir);
    // 获取modules目录下子目录
    const sub_dirs = fs.readdirSync(dirPath).filter((f) => {
        return fs.statSync(path.resolve(dirPath, f)).isDirectory();
    });
    return sub_dirs;
}

// 获取模块下子模块名称
const modules_subDir = getDirs('./modules');

// 当前模板的相对根路径
const rootPath = 'plop-templates/view';
// 输入的模块名称
const viewName = '{{viewName}}';
// tree编辑页面fields
const treeFields = ['treeId', 'treeName'];

/**
 * 用于测试文件生成
 */
function pushActionTest(actions, actionData) {
    actions.push({
        type: 'add',
        path: `modules/{{moduleName}}/views/${viewName}/test.vue`,
        templateFile: `${rootPath}/view-table/test.vue.hbs`,
        data: actionData
    });
}

/**
 * 添加布局页的生成和文件输出
 */
function pushActionLayoutView(actions, actionData) {
    // 添加文件 -- 主页面
    if (actionData.isSingleTable) {
        actions.push({
            type: 'add',
            path: `modules/{{moduleName}}/views/${viewName}/index.vue`,
            templateFile: `${rootPath}/view-table/index.vue.hbs`,
            data: actionData
        });
    } else {
        // tree+table 布局方式页面
        actions.push({
            type: 'add',
            path: `modules/{{moduleName}}/views/${viewName}/index.vue`,
            templateFile: `${rootPath}/view-table/treetable-index.vue.hbs`,
            data: actionData
        });
        actions.push({
            type: 'add',
            path: `modules/{{moduleName}}/views/${viewName}/components/Tree.vue`,
            templateFile: `${rootPath}/view-table/components/Tree.vue.hbs`,
            data: actionData
        });
    }
}

/**
 * 添加编辑相关页面
 */
function pushActionEditView(actions, actionData) {
    // 添加文件 -- 编辑页面
    if (actionData.isAdd || actionData.isEdit) {
        actions.push({
            type: 'add',
            path: `modules/{{moduleName}}/views/${viewName}/components/ListEditInfo.vue`,
            templateFile: `${rootPath}/view-table/components/ListEditInfo.vue.hbs`,
            data: actionData
        });
    }

    // 添加文件 -- Tree编辑页面
    if (actionData.isTreeAdd || actionData.isTreeEdit) {
        actions.push({
            type: 'add',
            path: `modules/{{moduleName}}/views/${viewName}/components/TreeEditInfo.vue`,
            templateFile: `${rootPath}/view-table/components/ListEditInfo.vue.hbs`,
            data: Object.assign({}, actionData, { servicePrefix: 'Tree', fieldList: treeFields })
        });
    }
}

/**
 * 添加多语言相关文件输出
 */
function pushActionLang(actions, actionData) {
    // 多语言要加上tree相关字典
    const langFields = actionData.fieldList.concat(treeFields);
    // 避免影响其他的action的data数据
    const langActionData = Object.assign({}, actionData, {
        fieldList: langFields
    });

    // 修改多语言 -- 中文
    actions.push({
        type: 'modify',
        path: `modules/{{moduleName}}/lang/zh.js`,
        pattern: /\s*}\s*\}\s*;?\s*$/gi,
        templateFile: `${rootPath}/lang/zh.js.hbs`,
        data: langActionData
    });

    // 增加路由名称多语言  -- 中文
    actions.push({
        type: 'modify',
        path: `modules/{{moduleName}}/lang/zh.js`,
        pattern: /\s*},\s*project\s*:\s*\{/gi,
        template: `,\n        ${viewName}: '新页面'\n    },\n    project: {`,
        data: langActionData
    });

    // 修改多语言 -- 英文
    actions.push({
        type: 'modify',
        path: `modules/{{moduleName}}/lang/en.js`,
        pattern: /\s*}\s*\}\s*;?\s*$/gi,
        templateFile: `${rootPath}/lang/en.js.hbs`,
        data: langActionData
    });

    // 增加路由名称多语言  -- 中文
    actions.push({
        type: 'modify',
        path: `modules/{{moduleName}}/lang/en.js`,
        pattern: /\s*},\s*project\s*:\s*\{/gi,
        template: `,\n        ${viewName}: 'new page'\n    },\n    project: {`,
        data: langActionData
    });
}

module.exports = {
    description: '创建一个新的模块',
    prompts: [
        {
            type: 'list',
            name: 'moduleName',
            choices: modules_subDir,
            message: '请选择所属模块! 注:也可在命令中指定模块名，如:plop view -- --moduleName frame'
        },
        {
            type: 'input',
            name: 'viewName',
            message: '请输入页面名称',
            validate: notEmpty('viewName')
        },
        {
            type: 'list',
            name: 'viewType',
            message: '请选择页面整体布局方式',
            choices: [
                {
                    name: '数据表格',
                    value: 'singleTable'
                },
                {
                    name: 'Tree + 数据表格',
                    value: 'treeTable'
                }
            ]
        },
        {
            type: 'checkbox',
            name: 'dataActionType',
            message: '请选择数据维护的功能',
            choices: (data) => {
                // 选项列表
                let choiceList = [
                    {
                        name: '列表-新增',
                        value: 'add',
                        checked: true
                    },
                    {
                        name: '列表-编辑',
                        value: 'edit',
                        checked: true
                    },
                    {
                        name: '列表-删除',
                        value: 'delete',
                        checked: true
                    }
                ];

                if (data.viewType === 'treeTable') {
                    choiceList = choiceList.concat([
                        {
                            name: 'Tree-新增',
                            value: 'treeAdd',
                            checked: true
                        },
                        {
                            name: 'Tree-编辑',
                            value: 'treeEdit',
                            checked: true
                        },
                        {
                            name: 'Tree-删除',
                            value: 'treeDelete',
                            checked: true
                        },
                        {
                            name: 'Tree-拖动',
                            value: 'treeSort',
                            checked: false
                        }
                    ]);
                }

                return choiceList;
            }
        },
        {
            type: 'input',
            name: 'fieldStr',
            message: '请输入数据列表字段，多个字段以","分割 注:"*"开头字段为查询字段'
        }
    ],
    actions: (data) => {
        // 执行动作列表
        const actions = [];

        // 处理输入字段参数，如果是空，则不进行分割
        const rawFields = data.fieldStr ? data.fieldStr.split(',') : [];
        // 字段第一个字符带*号，则标识需要查询字段
        const queryFields = rawFields.filter((item) => item.indexOf('*') === 0).map((item) => item.substr(1));
        // 所有输入字段，都做为编辑、展示字段
        let editFields = rawFields.map((item) => item.replace('*', ''));
        // 如果没有定义字段，则给默认值
        if (editFields.length === 0) {
            editFields = ['field1', 'field2'];
        }
        // 如果查询字段没有设置，默认取第一个
        if (queryFields.length === 0) {
            queryFields.push(editFields[0]);
        }

        // 模板通用参数
        const actionData = {
            // 所属模块名称
            moduleName: data.moduleName,
            // 页面目录名称
            viewName: data.viewName,
            // 服务接口名称前缀，用于增删改查方法
            // servicePrefix: data.viewType === 'treeTable' ? 'Tree' : '',
            // 是否单表格查询,false：tree+table方式。默认true
            isSingleTable: data.viewType !== 'treeTable',
            // 标识tree+table布局
            isTreeTable: data.viewType === 'treeTable',
            // 是否单字段查询，false：多字段查询方式。默认true
            // isSingleQuery: data.queryType !== 'multiField',
            isSingleQuery: queryFields.length === 1,
            // 是否包含新增功能
            isAdd: data.dataActionType.indexOf('add') >= 0,
            // 是否包含编辑功能
            isEdit: data.dataActionType.indexOf('edit') >= 0,
            // 是否包含删除功能
            isDelete: data.dataActionType.indexOf('delete') >= 0,
            // 是否包含Tree新增功能
            isTreeAdd: data.dataActionType.indexOf('treeAdd') >= 0,
            // 是否包含Tree编辑功能
            isTreeEdit: data.dataActionType.indexOf('treeEdit') >= 0,
            // 是否包含Tree删除功能
            isTreeDelete: data.dataActionType.indexOf('treeDelete') >= 0,
            // 是否包含Tree拖拽排序功能
            isTreeSort: data.dataActionType.indexOf('treeSort') >= 0,
            // 查询字段
            queryFields: queryFields,
            // 表格、编辑字典列表
            fieldList: editFields
        };
        // 如果是单字段查询，则字段名称直接作为字符串传入
        if (actionData.isSingleQuery) {
            actionData.queryFields = queryFields[0];
        }

        pushActionLayoutView(actions, actionData);

        pushActionEditView(actions, actionData);

        pushActionLang(actions, actionData);

        // 修改路由文件
        actions.push({
            type: 'modify',
            path: `modules/{{moduleName}}/router/index.js`,
            pattern: /\s*\]\s*\}\s*\]\s*;?\s*$/gi,
            templateFile: `${rootPath}/router/index.js.hbs`,
            data: actionData
        });

        // 修改service文件
        actions.push({
            type: 'modify',
            path: `modules/{{moduleName}}/service/index.js`,
            pattern: /\s*\}\s*;?\s*return\s+service\;\s*\}\s*;?\s*$/gi,
            templateFile: `${rootPath}/service/index.js.hbs`,
            data: actionData
        });

        return actions;
    }
};
