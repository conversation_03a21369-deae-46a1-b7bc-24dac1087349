<!-- 需求变更 -->
<template>
    <div class="view">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary" @click="confirm()" v-if="this.reasonType === 0">提交</el-button>
            <el-button type="primary" @click="handlereplace()" v-if="this.reasonType === 1">提交</el-button>
            <el-button type="info" @click="back()">返回</el-button>
        </div>
        <div class="content">
            <!-- 标题 -->
            <formula-title :title="getTitleByType()"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="120px" :rules="userRules">
                <el-form-item label="变更原因" prop="reasonType">
                    <el-select v-model="reasonType" @change="reasonChange()">
                        <el-option v-for="item in reasonData" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" prop="reason" v-if="this.reasonType === 0">
                    <el-input
                        type="textarea"
                        v-model="addForm.reason"
                        maxlength="500"
                        :rows="4"
                        placeholder="请输入变更原因"
                    ></el-input>
                </el-form-item>
                <el-form-item label="替换需求" v-if="this.reasonType === 1">
                    <el-select
                        v-model="replacedDemand"
                        placeholder="支持按需求名称、ID、来源编号搜索"
                        filterable
                        remote
                        :remote-method="remoteSearchRequire"
                        :loading="searchLoading"
                        clearable
                    >
                        <el-option
                            v-for="item in replaceRequireData"
                            :key="item.id"
                            :label="item.storyName"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <div class="add-flex">
                    <el-form-item label="需求名称" prop="storyName" v-if="this.reasonType === 0">
                        <el-input
                            v-model="addForm.storyName"
                            placeholder="请输入需求名称"
                            clearable
                            style="width: 520px"
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="类型" prop="storyType" v-if="this.reasonType === 0">
                        <el-select v-model="addForm.storyType" placeholder="请选择类型" clearable>
                            <el-option
                                v-for="item in typeData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="add-flex">
                    <el-form-item label="需求负责人" prop="owner" v-if="this.reasonType === 0">
                        <el-select
                            v-model="addForm.owner"
                            placeholder="请选择需求负责人"
                            clearable
                            multiple
                            filterable
                            @change="$forceUpdate()"
                        >
                            <el-option
                                v-for="item in ownerData"
                                :key="item.loginName"
                                :label="item.employeeName"
                                :value="item.loginName"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="期望交付日期" prop="deliveryDate" v-if="this.reasonType === 0">
                        <el-date-picker
                            v-model="addForm.deliveryDate"
                            type="date"
                            placeholder="请选择期望交付日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="优先级" prop="priority" v-if="this.reasonType === 0">
                        <el-select v-model="addForm.priority" placeholder="请选择优先级" clearable>
                            <el-option
                                v-for="item in priorityData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label="用户场景" prop="scene" v-if="this.reasonType === 0">
                    <el-input type="textarea" v-model="addForm.scene" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <el-form-item label="需求描述" prop="description" v-if="this.reasonType === 0">
                    <el-input type="textarea" v-model="addForm.description" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <el-form-item label="业务价值" prop="businessValue" v-if="this.reasonType === 0">
                    <el-input type="textarea" v-model="addForm.businessValue" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <el-form-item label="预期收益" prop="earnings" v-if="this.reasonType === 0">
                    <el-input type="textarea" v-model="addForm.earnings" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <el-form-item label="附件" prop="accessoryName" v-if="this.reasonType === 0">
                    <upload-file
                        :uploadUrl="uploadUrl"
                        :headers="headers"
                        :maxSize="maxSize"
                        :accept="accept"
                        :maxCount="maxCount"
                        :initialFileList="initialFileList"
                        @upload-success="handleUploadSuccess"
                        @remove-file="handleRemoveFile"
                        @init-file-ids="handleInitFileIds"
                    ></upload-file>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import Constant from 'dms/constant/dict.js';
import UploadFile from 'dms/components/UploadFile.vue';

const { typeData, priorityData } = Constant;

export default {
    name: 'ChangeRequire',
    components: { formulaTitle, UploadFile },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            replacedDemand: '',
            reasonType: 0,
            replaceRequireData: [],
            searchLoading: false,
            addForm: {
                reason: '',
                id: '',
                storyName: '',
                storyType: '',
                owner: [],
                ownerName: [],
                deliveryDate: '',
                priority: '',
                scene: '',
                description: '',
                businessValue: '',
                earnings: ''
            },
            reasonData: [
                {
                    label: '常规变更',
                    value: 0
                },
                {
                    label: '替换为其它来源需求',
                    value: 1
                }
            ],
            ownerData: [],
            typeData,
            priorityData,

            // 上传
            headers: {
                Accept: 'application/json, text/plain, */*',
                Authorization: `Bearer ${this.$tools.getToken()}`
            },
            maxSize: 20,
            accept: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'pdf', 'pptx', 'doc', 'docx', 'xlsx', 'zip', '7z', 'rar'],
            maxCount: 5,
            row: {},
            fileList: [],
            fileId: [],
            initialFileList: [],
            userRules: {
                reason: [
                    {
                        required: true,
                        message: '请输入变更原因',
                        trigger: 'blur'
                    }
                ],
                storyName: [
                    {
                        required: true,
                        message: '请输入需求名称',
                        trigger: 'change'
                    }
                ],
                storyType: [
                    {
                        required: true,
                        message: '请选择类型',
                        trigger: 'change'
                    }
                ],
                owner: [
                    {
                        required: true,
                        message: '请选择需求负责人',
                        trigger: 'change'
                    }
                ],
                deliveryDate: [
                    {
                        required: true,
                        message: '请选择期望交付日期',
                        trigger: 'change'
                    }
                ],
                priority: [
                    {
                        required: true,
                        message: '请选择优先级',
                        trigger: 'change'
                    }
                ],
                scene: [
                    {
                        required: true,
                        message: '请输入用户场景',
                        trigger: 'blur'
                    }
                ],
                description: [
                    {
                        required: true,
                        message: '请输入需求描述',
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    computed: {
        uploadUrl() {
            return this.$service.dms.original.uploadFile();
        }
    },
    mounted() {
        this.row = this.$route.query;
        this.getEmployees(this.row);
        this.getStoryInfo(this.row);
        this.getFile(this.row);
    },
    methods: {
        getTitleByType() {
            return '需求变更';
        },
        reasonChange() {
            this.$nextTick(() => {
                if (this.reasonType === 0) {
                    this.getEmployees(this.row);
                    this.getStoryInfo(this.row);
                    this.getFile(this.row);
                } else {
                    this.replaceRequireData = [];
                    this.replacedDemand = '';
                }
                if (this.$refs.dataForm) {
                    this.$refs.dataForm.resetFields();
                }
            });
        },
        // 远程搜索需求
        remoteSearchRequire(query) {
            if (!query) {
                this.replaceRequireData = [];
                return;
            }

            this.searchLoading = true;
            const params = {
                key: query
            };

            this.$service.dms.original
                .getStoryName(params)
                .then((res) => {
                    this.searchLoading = false;
                    if (res.code === '0000') {
                        this.replaceRequireData = res.data || [];
                    } else {
                        this.$message.error(res.message);
                        this.replaceRequireData = [];
                    }
                })
                .catch(() => {
                    this.searchLoading = false;
                    this.replaceRequireData = [];
                });
        },
        // 查询需求负责人
        getEmployees() {
            const params = {
                isAll: '1'
            };
            params.status = this.$service.dms.original.getEmployees(params).then((res) => {
                if (res.code === '0000') {
                    this.ownerData = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 查询文档
        getFile(row) {
            const params = {
                id: row.demandId
            };
            params.status = this.$service.dms.original.getFile(params).then((res) => {
                if (res.code === '0000') {
                    this.fileList = res.data || [];
                    // 设置附件列表用于反显
                    this.initialFileList = this.fileList;
                    this.$emit('update:fileList', this.fileList);
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 查询详情
        getStoryInfo(row) {
            const params = {
                id: row.demandId
            };
            params.status = this.$service.dms.original.getStoryInfo(params).then((res) => {
                if (res.code === '0000') {
                    const { data } = res;
                    this.$nextTick(() => {
                        this.addForm = {
                            id: data.baseInfo.id,
                            storyName: data.baseInfo.storyName,
                            version: data.baseInfo.version,
                            storyType: data.baseInfo.storyType,
                            deliveryDate: data.baseInfo.deliveryDate,
                            priority: data.baseInfo.priority,
                            description: data.extendInfo.description,
                            scene: data.extendInfo.scene,
                            businessValue: data.extendInfo.businessValue,
                            earnings: data.extendInfo.earnings,
                            resson: ''
                        };
                        this.addForm.owner = data.baseInfo.owner.replace(/^,|,$/g, '').split(',');
                        this.addForm.ownerName = data.baseInfo.ownerName.split(',');
                    });
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        handleUploadSuccess(res, file, fileList) {
            // 上传成功后的处理逻辑
            if (res.code === '0000') {
                this.$message.success(res.message);
                this.fileId.push(res.data);
            } else {
                this.$message.error(res.message);
            }
        },
        handleRemoveFile(file) {
            // 文件删除后的处理逻辑
            if (file.response && file.response.data) {
                const id = file.response.data;
                this.fileId = this.fileId.filter((item) => item !== id);
            } else if (file.id) {
                // 处理初始文件的删除
                this.fileId = this.fileId.filter((item) => item !== file.id);
            }
        },
        // 处理初始文件ID列表
        handleInitFileIds(fileIds) {
            this.fileId = [...fileIds];
        },

        // 保存
        confirm() {
            this.$refs.dataForm.validate((valid) => {
                if (valid) {
                    const params = {
                        ...this.addForm,
                        ownerName: this.addForm.owner
                            .map((id) => {
                                const employee = this.ownerData.find((item) => item.loginName === id);
                                return employee ? employee.employeeName : null;
                            })
                            .filter(Boolean)
                            .join(','),
                        owner: `${this.addForm.owner.map((id) => `,${id}`).join('')},`,
                        fileId: this.fileId
                    };

                    this.$service.dms.original.changeRequire(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success(res.message);
                            history.go(-1);
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                }
            });
        },
        handlereplace() {
            if (!this.replacedDemand) {
                this.$message.error('请选择要替换的需求');
                return;
            }
            const params = {
                storyId: this.row.demandId,
                newStoryId: this.replacedDemand,
                allCount: this.row.allCount
            };
            this.$service.dms.original.changeStory(params).then((res) => {
                if (res.code === '0000') {
                    this.$message.success(res.message);
                    history.go(-1);
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        back() {
            history.go(-1);
        }
    }
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.add-flex {
    display: flex;
    justify-content: flex-start;
}
.content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    margin-top: 10px;
}
</style>
