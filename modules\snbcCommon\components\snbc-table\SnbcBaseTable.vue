<template>
    <div class="table-content query-label-line2">
        <snbc-table-query
            v-if="showTableQuery"
            :query-params="tableConfig.queryParams"
            :query-config="tableConfig.queryConfig"
            @query="handleQuery"
            @reset="handleReset"
            @seniorQuery="handleSeniorQuery"
        />
        <snbc-table-header
            v-if="showTableHeader"
            :config="{
                headerButtons: tableConfig.headerButtons,
                headerTitle: tableConfig.headerTitle
            }"
            :selections="selections"
            :columns="tableConfig.elTableColumns"
        />
        <slot name="tabs" />
        <slot name="table-info-top" />
        <snbc-table-list
            :config="tableConfig"
            :list="list"
            :selections="selections"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortChange"
        >
            <template v-for="column in columnsWithSlot" #[column.slot]="slotScope">
                <slot :name="column.slot" v-bind="slotScope"></slot>
            </template>
        </snbc-table-list>
        <template v-if="hasPage">
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="tableConfig.pageParams.currentPage"
                :limit.sync="tableConfig.pageParams.pageSize"
                @pagination="queryList"
            />
        </template>
    </div>
</template>
<script>
import objectFactory from 'snbcCommon/common/object-factory.js';
import SnbcTableQuery from 'snbcCommon/components/snbc-table/SnbcTableQuery.vue';
import SnbcTableHeader from 'snbcCommon/components/snbc-table/SnbcTableHeader.vue';
import SnbcTableList from 'snbcCommon/components/snbc-table/SnbcTableList.vue';

export default {
    name: 'SnbcBaseTable',
    components: {
        SnbcTableQuery,
        SnbcTableHeader,
        SnbcTableList
    },
    props: {
        /**
         * 表格相关配置项
         */
        tableConfig: {
            type: Object,
            default() {
                return {
                    // 查询参数对象
                    queryParams: {},
                    // 分页查询参数
                    pageParams: objectFactory('pageParams'),
                    // 排序参数
                    sortParams: objectFactory('sortParams'),
                    // 查询区域表单配置
                    queryConfig: {
                        items: []
                    },
                    // 查询api
                    queryApi: null,
                    // 列配置
                    elTableColumns: [],
                    // 行操作
                    operations: [],
                    // 表格头部按钮
                    headerButtons: [],
                    // 启用列选择
                    selectionAble: true
                };
            }
        },
        shouldQueryList: {
            type: Boolean,
            default: true
        },
        // 是否展示列选择及标题
        showTableHeader: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            // 列表是否已查询
            queried: false,
            total: 0,
            list: [{}, {}],
            // 多选项
            selections: []
        };
    },
    computed: {
        // 查询区域展示控制
        showTableQuery() {
            return this.tableConfig?.queryConfig?.items?.length > 0;
        },
        // 是否展示分页
        hasPage() {
            return this.tableConfig?.hasPage !== false;
        },
        // 过滤出有 slot 的列
        columnsWithSlot() {
            return this.tableConfig.elTableColumns.filter((column) => column.slot);
        }
    },
    created() {
        // 设置分页参数
        if (!this.tableConfig.pageParams) {
            this.tableConfig.pageParams = objectFactory('pageParams');
        }
        // 设置排序参数
        if (!this.tableConfig.sortParams) {
            this.tableConfig.sortParams = objectFactory('sortParams');
        }
        // 默认排序
        const { sortKey, sortOrder } = this.tableConfig.sortParams;
        if (sortKey && sortOrder) {
            if (!this.tableConfig.elTableAttrs) {
                this.tableConfig.elTableAttrs = {};
            }
            this.tableConfig.elTableAttrs.defaultSort = {
                prop: sortKey,
                order: sortOrder
            };
        }
    },
    methods: {
        // 查询操作，第一页查询
        handleQuery() {
            if (this.hasPage) {
                this.$set(this.tableConfig.pageParams, 'currentPage', 1);
            }
            if (this.shouldQueryList) {
                this.queryList();
            }
            this.$emit('query');
        },
        // 重置参数操作
        handleReset() {
            for (const key in this.tableConfig.queryParams) {
                if (Object.prototype.hasOwnProperty.call(this.tableConfig.queryParams, key)) {
                    this.resetValueByKey(key);
                }
            }
            // 重置查询
            if (this.tableConfig?.hooks?.resetSearch) {
                this.tableConfig.hooks.resetSearch(this.tableConfig.queryParams);
            }
            this.handleQuery();
        },
        // 列表数据查询
        async queryList() {
            const params = {
                ...this.tableConfig.queryParams,
                ...this.tableConfig.pageParams,
                ...this.tableConfig.sortParams
            };
            // 列表查询参数hook处理
            if (this.tableConfig?.hooks?.queryParamsHook) {
                this.tableConfig.hooks.queryParamsHook(params);
            }
            try {
                const res = await this.tableConfig.queryApi(params);
                let code;
                let result;
                let message;
                // 根据不同res结构获取数据
                if (res?.head && res?.body) {
                    ({ code, message } = res.head);
                    result = res.body;
                } else {
                    ({ code, result, message } = res);
                }
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                let { list } = result;
                const { total, startRow } = result;
                if (!list) {
                    list = result;
                }
                // 列表数据hook处理
                if (this.tableConfig?.hooks?.tableListHook) {
                    this.tableConfig.hooks.tableListHook(list, total);
                }
                // 添加序号
                if (startRow) {
                    list.map((item, index) => {
                        item.index = startRow + index;
                        return item;
                    });
                }
                this.list = list;
                this.total = total;
                this.queried = true;
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 当选择项发生变化时触发
        handleSelectionChange(selections) {
            this.tableConfig.handleSelectionChange && this.tableConfig.handleSelectionChange(selections);
            this.selections = selections;
        },
        // 排序条件发生变化
        handleSortChange(params) {
            const { column, prop: sortKey, order: sortOrder } = params;

            if (column.sortable === 'custom' && sortOrder) {
                this.$set(this.tableConfig.sortParams, 'sortKey', sortKey);
                this.$set(this.tableConfig.sortParams, 'sortOrder', sortOrder);
            } else {
                this.$set(this.tableConfig.sortParams, 'sortKey', '');
                this.$set(this.tableConfig.sortParams, 'sortOrder', '');
            }
            if (this.tableConfig?.hooks?.sortChangeHook) {
                this.tableConfig.hooks.sortChangeHook(params);
            }
            if (column.sortable === 'custom') {
                this.handleQuery();
            }
        },
        // 高级查询处理 参数
        handleSeniorQuery(type) {
            if (type) return;
            const keys = this.tableConfig.queryConfig.items.map((item) => item.modelKey).slice(3);

            for (const key of keys) {
                this.resetValueByKey(key);
            }
        },
        // 重置 查询数据
        resetValueByKey(key) {
            const resetValMap = {
                '[object Undefined]': undefined,
                '[object Number]': undefined,
                '[object String]': '',
                '[object Array]': []
            };
            const paramType = Object.prototype.toString.call(this.tableConfig.queryParams[key]);
            let resetVal = resetValMap[paramType];
            if (!Object.keys(resetValMap).includes(paramType)) {
                resetVal = null;
            }
            this.$set(this.tableConfig.queryParams, key, resetVal);
        }
    }
};
</script>
<style lang="scss" scoped>
.table-content {
    height: 100%;
    ::v-deep .pagination-container {
        padding: 5px 0px;
        margin-top: 0;
        .el-pagination {
            margin-top: 8px;
        }
    }
}
::v-deep.el-table th {
    background-color: #3370ff !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep .el-table--mini .el-table__cell {
    padding: 0px !important;
}
::v-deep .el-table .el-table__row {
    height: 35px !important;
}
::v-deep .el-table .has-gutter {
    height: 40px !important;
}
</style>
