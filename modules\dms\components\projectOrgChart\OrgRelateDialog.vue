<template>
    <el-dialog title="关联小组" :visible.sync="dialogVisible" width="500px" height="300px" @close="closeDialog">
        <el-form ref="form" :model="form" label-width="80px" :rules="rules">
            <el-form-item label="小组" prop="teamId">
                <OrgDepartmentSelector v-model="form.teamId"></OrgDepartmentSelector>
            </el-form-item>
        </el-form>
        <div class="confirm-button">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">保 存</el-button>
        </div>
    </el-dialog>
</template>

<script>
import OrgDepartmentSelector from './OrgDepartmentSelector';

export default {
    name: 'OrgRelateDialog',
    components: { OrgDepartmentSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        objectInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            form: {
                teamId: []
            },
            rules: {
                teamId: [{ required: true, message: '请选择小组' }]
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.form = {
                account: '',
                groupName: '',
                groupLeader: ''
            };
            this.$refs.form.resetFields();
            this.dialogVisible = false;
        },
        /**
         * 验证表单
         * @return {Promise} 验证是否通过
         */
        validate() {
            return this.$refs.form.validate();
        },
        /**
         * 确认
         */
        async handleSubmit() {
            const isValidate = await this.validate();

            if (!isValidate) {
                return;
            }
            const params = {
                teamId: this.form.teamId[2],
                ...this.objectInfo
            };
            const api = this.$service.dms.group.relateTeamToProjectOrGroup;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('保存成功');
                this.closeDialog();
                this.$emit('success');
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
// 预先定义表格行最小高度，避免布局抖动
::v-deep .el-table .el-table__row {
    min-height: 60px;
}

.progress-input-container {
    display: flex;
    align-items: center;
    justify-content: center;

    .percent-sign {
        margin-left: 2px;
        font-size: 14px;
    }
}
.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 25px;
}

// 添加loading时的样式覆盖
::v-deep .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.5);
}
</style>
