<template>
    <el-form-item v-bind="elFormItemAttrs">
        <DepartmentSelector
            class="selector"
            v-model="config.modelObj[config.modelKey]"
            v-bind="elSelectorAttrs"
            @input="handleChange"
        >
        </DepartmentSelector>
    </el-form-item>
</template>
<script>
import DepartmentSelector from 'dms/components/DepartmentSelector';

export default {
    name: 'SnbcFormDepartmentSelector',
    components: { DepartmentSelector },
    props: {
        /**
         * SnbcFormDepartmentSelector组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elSelectorAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // DepartmentSelector组件默认属性设置
            defaultElSelectorAttrs: {}
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // DepartmentSelector组件应用属性
        elSelectorAttrs() {
            return {
                ...this.defaultElSelectorAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elSelectorAttrs || {})
            };
        }
    },
    methods: {
        // 选择数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
<style scoped>
.selector {
    width: 100%;
    font-weight: initial;
}
</style>
