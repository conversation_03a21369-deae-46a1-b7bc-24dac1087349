/**
 * 中文国际化配置：项目模块国际化配置文件route和project必填、httpCode可选
 * @param {Object} route 项目模块路由国际化
 * @param {Object} httpCode 项目模块httpCode国际化
 * @param {Object} project 项目模块除路由、httpCode外，其他信息国际化
 */
export default {
    route: {
        userManagement: '用户管理',
        personManagement: '人员管理',
        changePwd: '修改密码'
    },
    project: {
        systmeTitle: '研发数据管理平台',
        changePwd: '修改密码',
        msg: {
            handling: '正在处理，请稍后...'
        }
    },
    httpCode: {
        http401: '无权限或者权限已经过期，请重新登录',
        http404: '找不到接口',
        http500: '无法访问服务器'
    }
};
