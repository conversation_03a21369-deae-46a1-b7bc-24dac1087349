<template>
    <div>
        <el-dialog :title="getTitle()" :visible.sync="localVisible" width="60%" class="custom-dialog" @close="cancel">
            <el-form :model="addForm" ref="dataForm" label-width="120px" :rules="userRules">
                <el-form-item label="变更原因" prop="changeReason" v-if="this.type !== 'edit'">
                    <el-input type="textarea" v-model="addForm.changeReason" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <div class="add-flex">
                    <el-form-item label="需求名称" prop="storyName">
                        <el-input
                            v-model="addForm.storyName"
                            placeholder="请输入需求名称"
                            maxlength="64"
                            :disabled="this.type === 'edit'"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="优先级" prop="priority">
                        <el-select v-model="addForm.priority" placeholder="请选择优先级" clearable>
                            <el-option
                                v-for="item in priorityData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="add-flex">
                    <el-form-item label="产品模块" prop="module" v-if="this.row.demandClass === '产品需求'">
                        <el-select v-model="addForm.module" placeholder="请选择产品模块" clearable filterable>
                            <el-option v-for="item in moduleData" :key="item.id" :label="item.path" :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="期望交付日期" prop="deliveryDate">
                        <el-date-picker
                            v-model="addForm.deliveryDate"
                            type="date"
                            placeholder="请选择期望交付日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                </div>
                <el-form-item label="需求等级" prop="storyLevel">
                    <el-select v-model="addForm.storyLevel" placeholder="请选择需求等级" clearable>
                        <el-option
                            v-for="item in storyLevelData"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="需求描述" prop="description">
                    <el-input
                        type="textarea"
                        v-model="addForm.description"
                        maxlength="500"
                        :rows="4"
                        :disabled="this.type === 'edit'"
                    ></el-input>
                </el-form-item>
                <el-form-item label="验收标准" prop="verify">
                    <el-input
                        type="textarea"
                        v-model="addForm.verify"
                        maxlength="500"
                        :rows="4"
                        :disabled="this.type === 'edit'"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="cancel()">取消</el-button>
                <el-button type="primary" @click="confirm()">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import Constant from 'dms/constant/dict.js';

const { priorityData, storyLevelData } = Constant;
export default {
    name: 'CreateRequirePop',
    data() {
        return {
            // 弹窗信息
            localVisible: false,
            addForm: {
                storyName: '',
                priority: '',
                deliveryDate: '',
                storyLevel: '',
                description: '',
                verify: '',
                id: '',
                module: ''
            },
            // 传参数据
            type: '',
            row: {},
            // 优先级
            priorityData,
            // 需求等级
            storyLevelData,
            // 产品模块
            moduleData: []
        };
    },
    computed: {
        userRules() {
            if (this.type === 'edit') {
                return {
                    deliveryDate: [
                        {
                            required: true,
                            message: '请选择期望交付时间',
                            trigger: 'change'
                        }
                    ],
                    priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
                    module: [{ required: true, message: '请选择产品模块', trigger: 'change' }],
                    storyLevel: [{ required: true, message: '请选择需求等级', trigger: 'change' }]
                };
            }
            return {
                changeReason: [
                    {
                        required: true,
                        message: '请输入变更原因',
                        trigger: 'blur'
                    }
                ],
                storyName: [
                    {
                        required: true,
                        message: '请输入需求名称',
                        trigger: 'change'
                    }
                ],
                priority: [
                    {
                        required: true,
                        message: '请选择优先级',
                        trigger: 'change'
                    }
                ],
                module: [
                    {
                        required: true,
                        message: '请选择产品模块',
                        trigger: 'change'
                    }
                ],
                deliveryDate: [
                    {
                        required: true,
                        message: '请选择期望交付时间',
                        trigger: 'change'
                    }
                ],
                storyLevel: [
                    {
                        required: true,
                        message: '请选择需求等级',
                        trigger: 'change'
                    }
                ],
                description: [
                    {
                        required: true,
                        message: '请输入需求描述',
                        trigger: 'blur'
                    }
                ],
                verify: [
                    {
                        required: true,
                        message: '请输入验收标准',
                        trigger: 'blur'
                    }
                ]
            };
        }
    },
    methods: {
        // 打开弹窗
        open(row, type) {
            this.row = row;
            this.type = type;
            this.getModuleData();
            this.$nextTick(() => {
                this.addForm = {
                    storyName: row.demandName,
                    priority: row.priority,
                    deliveryDate: row.expectedDate,
                    storyLevel: row.demandLevel,
                    description: row.description,
                    verify: row.verify,
                    id: row.demandId,
                    module: row.productModuleId
                };
                this.$refs.dataForm && this.$refs.dataForm.resetFields();
            });
            this.localVisible = true;
        },
        // 查询产品模块
        getModuleData() {
            const params = {
                productId: Number(this.row.assProductId)
            };
            params.status = this.$service.dms.original.getProductModule(params).then((res) => {
                if (res.code === '0000') {
                    this.moduleData = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        getTitle() {
            if (this.type === 'edit') {
                return '编辑产品需求';
            }
            return '产品需求变更';
        },
        confirm() {
            this.$refs.dataForm.validate((valid) => {
                if (!valid) return;
                const params =
                    this.type === 'edit'
                        ? this.addForm
                        : {
                              demandId: this.addForm.id,
                              demandName: this.addForm.storyName,
                              priority: this.addForm.priority,
                              description: this.addForm.description,
                              verify: this.addForm.verify,
                              changeReason: this.addForm.changeReason,
                              deliveryDate: this.addForm.deliveryDate,
                              storyLevel: this.addForm.storyLevel,
                              moduleId: this.addForm.module,
                              checkType: '内容变更',
                              demandVersion: this.row.demandVersion
                          };
                const selectedModule = this.moduleData.find((item) => item.id === this.addForm.module);
                if (selectedModule) {
                    params.moduleName = selectedModule.path;
                }
                const serviceCall =
                    this.type === 'edit'
                        ? this.$service.dms.original.editStory(params)
                        : this.$service.dms.original.changeConfirm(params);

                serviceCall.then((res) => {
                    if (res.code === '0000') {
                        this.$message.success(res.message);
                        this.localVisible = false;
                        this.$parent.query();
                    } else {
                        this.$message.error(res.message);
                    }
                });
            });
        },
        // 关闭弹窗
        cancel() {
            this.$nextTick(() => {
                this.localVisible = false;
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.add-flex {
    display: flex;
    justify-content: space-between;
}
</style>
