<template>
    <el-form label-width="80px" style="width: 30%">
        <el-form-item label="团队列表">
            <el-select
                v-model="selectedTeam"
                filterable
                placeholder="请选择团队"
                :filter-method="handleSearch"
                ref="teamListRef"
                @click.stop
            >
                <!-- 自定义下拉内容 -->
                <el-option :value="selectedTeam" :label="selectedLabel" style="height: auto; padding: 0">
                    <div class="custom-select-dropdown">
                        <!-- 搜索框 -->
                        <div class="search-box">
                            <el-input
                                v-model="searchText"
                                @click.native.stop
                                placeholder="搜索团队"
                                clearable
                                @input="handleSearch"
                            >
                                <i class="el-icon-search el-input__icon" slot="prefix"> </i>
                            </el-input>
                        </div>
                        <!-- Tab 分类 -->
                        <el-tabs v-model="activeTab" @click.native.stop @tab-click="handleTabChange">
                            <el-tab-pane
                                v-for="group in filteredList"
                                :key="group.value"
                                :label="group.label"
                                :name="group.value"
                            >
                                <!-- 成员列表 -->
                                <ul class="team-list">
                                    <li
                                        v-for="team in group.children"
                                        :key="team.value"
                                        :class="{ active: selectedTeam === team.value }"
                                        @click="handleSelect(team)"
                                    >
                                        {{ team.label }}
                                    </li>
                                </ul>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </el-option>
            </el-select>
        </el-form-item>
    </el-form>
</template>

<script>
export default {
    name: 'TeamListSelect',
    components: {},
    data() {
        return {
            list: [
                {
                    label: '我参与',
                    value: 'myself',
                    children: [
                        { label: '系研新员工管理专项', value: 'fe-1' },
                        { label: 'DMS团队', value: 'fe-2' }
                    ]
                },
                {
                    label: '其他',
                    value: 'others',
                    children: [
                        { label: '集成驱动交付团队', value: 'be-1' },
                        { label: '昆仑智拣沧海团队', value: 'be-2' }
                    ]
                }
            ],
            // 当前选中的成员 value
            selectedTeam: 'fe-1',
            // 当前选中的成员 label
            selectedLabel: '系研新员工管理专项',
            // 默认激活的 Tab
            activeTab: 'myself',
            // 搜索关键词
            searchText: ''
        };
    },
    computed: {
        // 根据搜索关键词过滤列表
        filteredList() {
            return this.list.map((group) => ({
                ...group,
                children: group.children.filter((team) =>
                    team.label.toLowerCase().includes(this.searchText.toLowerCase())
                )
            }));
        }
    },
    methods: {
        // Tab 切换时更新当前分组
        handleTabChange(tab) {
            this.activeTab = tab.name;
        },
        // 选择成员
        handleSelect(team) {
            this.selectedTeam = team.value;
            this.selectedLabel = team.label;
            this.$refs.teamListRef.blur();
            this.$emit('change', team);
        },
        // 搜索过滤
        handleSearch(query) {
            this.searchText = query;
            // 搜索时自动切换到第一个有结果的分组
            const validGroup = this.filteredList.find((group) => group.children.length > 0);
            if (validGroup) this.activeTab = validGroup.value;
        },
        submitInfo() {
            console.log(this.selectedTeam);
        }
    }
};
</script>

<style lang="scss" scoped>
/* 自定义下拉框样式 */
.custom-select-dropdown {
    padding: 10px;
    width: 100%;
    background-color: #fff;
}

.team-list {
    max-height: 300px;
    overflow-y: auto;
    list-style: none;
    padding: 0;
    margin: 0;

    li {
        padding: 2px;
        cursor: pointer;
        transition: all 0.3s;
        color: #333;
        font-weight: 500;
    }

    li:hover {
        background-color: #f5f7fa;
    }

    li.active {
        color: #409eff;
        background-color: #ecf5ff;
    }
}
</style>
