<template>
    <div class="group-management-list">
        <div class="content query-label-line2">
            <group-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="loadProductData"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @selection-change="handleSelectionChange"
                @pagination="handlePagination"
            >
                <template #rightNav>
                    <el-button type="text" @click="createDemand" class="create-demand-button"
                        ><i class="el-icon-plus"></i> 提需求
                    </el-button>
                </template>
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                </template>
            </group-list>
        </div>
        <GroupUpdateDialog :visible.sync="groupUpdateDialogVisible"></GroupUpdateDialog>
    </div>
</template>

<script>
import GroupList from 'dms/views/groupManagement/components/groupList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import GroupUpdateDialog from './GroupUpdateDialog.vue';

export default {
    name: 'GroupManagementList',
    components: {
        GroupList,
        GroupUpdateDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: 'all',
            // 导航栏配置
            navItems,
            // 查询参数数
            params:{},
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10,
            groupUpdateDialogVisible: false
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        createDemand() {
            this.groupUpdateDialogVisible = true;
        },
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams
                };

                // 日期处理
                if (params.foundDate && params.foundDate.length > 0) {
                    params.startDateString = params.foundDate[0];
                    params.endDateString = params.foundDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closeStartDateString = params.closeDate[0];
                    params.closeEndDateString = params.closeDate[1];
                }

                const response = await this.$service.dms.common.getProjectOrGroupList(params);

                if (response.code === '000000') {
                    this.productData = response.result?.list || [];
                    this.total = response.result?.total || 0;
                } else {
                    this.$message.error(response.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理重置
        handleReset() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的产品:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },

        // 编辑产品
        handleEdit(row) {},

        // 查看产品详情
        handleView(row) {},

        // 删除产品
        async handleDelete(row) {
            await this.$confirm(`确定要删除产品"${row.productName}"吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });
        },

        /**
         * 新增
         */
        handleAdd() {
            this.$router.push({
                name: 'AddProduct'
            });
        },
        handleEdit(row) {}
    }
};
</script>

<style lang="scss" scoped>
.group-management-list {
    padding: 20px;
}
.create-demand-button {
    margin-right: 20px;
}
</style>
