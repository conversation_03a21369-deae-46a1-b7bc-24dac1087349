<template>
    <div class="work-time-bar">
        <el-popover
            popper-class="resources-workTimeBar--popper"
            placement="bottom"
            width="320"
            :disabled="workHour === '0.0' || taskList.taskShowVoList === null"
            ref="popover"
        >
            <div class="popoverContent">
                <el-skeleton :rows="4" animated :loading="loading" :throttle="1500">
                    <template>
                        <div class="taskContainer" v-for="(item, index) in taskDetail" :key="index">
                            <WorkTimeDetailList
                                :index="index"
                                :item="item"
                                :taskDetail="taskDetail"
                                :view="view"
                            ></WorkTimeDetailList>
                        </div>
                    </template>
                </el-skeleton>
            </div>
            <!-- 正常工作的情况 -->
            <el-button
                v-show="!zeroWorkHour && workHour !== -1 && !isLast"
                slot="reference"
                :type="type"
                :style="{
                    'height': '30px',
                    'min-width': view === 'day' ? '25px' : '60px',
                    'cursor': 'pointer'
                }"
                @click="handleClick"
                :class="abnormalWorkHoursClass"
                :title="computedTooltip"
            >
                <div>{{ workHour }}</div>
            </el-button>
            <!-- 汇总 -->
            <div
                slot="reference"
                v-show="!zeroWorkHour && workHour !== -1 && isLast"
                :style="{
                    'min-width': view === 'day' ? '25px' : '60px'
                }"
                class="specialHour"
            >
                {{ workHour }}
            </div>
            <!-- 工时为 0 的情况 -->
            <div
                slot="reference"
                v-show="zeroWorkHour && workHour !== -1"
                :style="{
                    'min-width': view === 'day' ? '25px' : '60px',
                    'color': '#333'
                }"
                class="specialHour"
                :class="abnormalWorkHoursClass"
                :title="computedTooltip"
            >
                {{ this.taskList.workStatus === '正常' || this.isLast ? '-' : '' }}
            </div>
            <div
                slot="reference"
                v-show="workHour === -1"
                :class="abnormalWorkHoursClass === 'on-business' ? 'on-business' : ''"
                :title="computedTooltip"
            ></div>
        </el-popover>
    </div>
</template>
<script>
import WorkTimeDetailList from 'dms/components/WorkTimeDetailList.vue';

export default {
    name: 'WorkTimeBar',
    components: {
        WorkTimeDetailList
    },
    props: {
        view: { type: String, default: 'day' },
        taskList: { type: Object, default: () => ({ taskShowVoList: [] }) },
        isLast: { type: Boolean, default: false },
        isPlanHour: { type: Boolean, default: false }
    },
    data() {
        return {
            // 任务详情
            taskDetail: []
        };
    },
    computed: {
        // 分配的工作时长可以为0，如果为null，则不显示
        // 为null表示这天休息，本来就不用填工时
        workHour() {
            if (this.isPlanHour) {
                return this.taskList.planHours ?? -1;
            }
            return this.taskList.hours ?? -1;
        },
        loading() {
            return this.taskDetail.length === 0;
        },
        // 显示柱状图样式的逻辑
        type() {
            if (this.view === 'day') {
                if (this.workHour > 12) {
                    return 'danger';
                } else if (this.workHour <= 8) {
                    return 'success';
                }
            } else if (this.view === 'month') {
                if (this.workHour > 252) {
                    return 'danger';
                } else if (this.workHour <= 168) {
                    return 'success';
                }
            }
            return 'warning';
        },
        // 动态计算高度
        buttonHeight() {
            if (this.workHour === -1) {
                return 0;
            }
            return 30;
        },
        // 工时是否为 0
        zeroWorkHour() {
            return this.workHour === '0' || this.workHour === '0.0';
        },
        // 异常工时对应的样式(只有日视图生效)
        abnormalWorkHoursClass() {
            if (this.view === 'month') {
                return '';
            }
            const map = {
                正常: '',
                请假: 'on-leave',
                出差: 'on-business'
            };
            return map[this.taskList.workStatus || '正常'];
        },
        // 休假与出差的提示语
        computedTooltip() {
            const map = {
                '': '',
                'on-leave': '请假',
                'on-business': '出差'
            };
            return map[this.abnormalWorkHoursClass];
        }
    },
    watch: {
        taskList: {
            handler(newVal) {
                this.taskDetail = newVal.taskShowVoList ?? [];
            },
            deep: true
        },
        taskDetail(newVal) {
            if (newVal.length !== 0) {
                this.$nextTick(() => {
                    this.$refs.popover.updatePopper();
                });
            }
        }
    },
    methods: {
        handleClick() {
            this.$emit('click');
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.work-time-bar {
    height: 30px;
    display: flex;
    .el-button {
        font-weight: bold;
        color: #fff;
        padding: 0px;
        min-width: 20px;
        font-size: 10px;
        text-align: center;
        margin-top: auto;
    }
    span {
        display: inline-block !important;
    }
}

.success {
    background-color: #67c23a;
}
.danger {
    background-color: #f56c6c;
}
.warning {
    background-color: #e6a23c;
}
.specialHour {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 11px;
    font-weight: 600;
    line-height: 30px;
}

.work-hour-dialog {
    color: #409eff;
    width: 40px;
    white-space: nowrap;
    margin-left: auto;
}
.task-title {
    margin-left: 25px;
    width: fit-content;
}
@mixin work-status($status) {
    &::after {
        content: $status;
        position: absolute;
        top: 1px;
        right: 1px;
        line-height: 1;
        color: #022777;
        background-color: rgba(255, 255, 0, 0.5);
        padding: 1px;
        border-radius: 50rem;
        font-weight: 500;
        transition: transform 0.2s ease;
        cursor: pointer;
    }

    &:hover::after {
        transform: scale(1.5);
    }
}
.on-leave {
    @include work-status('假');
    font-size: 9px;
    @include respond-to('xl') {
        font-size: 10px;
    }
}
.on-business {
    @include work-status('差');
    font-size: 9px;
    @include respond-to('xl') {
        font-size: 10px;
    }
}
</style>
<style lang="scss">
// 弹窗样式，与app同级，只能写在这里
.el-popper.el-popover.resources-workTimeBar--popper {
    max-height: 360px;
    overflow: auto;
    width: 420px !important;
}
</style>
