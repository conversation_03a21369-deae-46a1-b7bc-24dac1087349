{
    // 保持的时候格式化
    "editor.formatOnSave": true,
    // 配置格式化工具未prettier
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    // 空格显示为虚点
    "editor.renderWhitespace": "all",
    "editor.tabSize": 4,
    // 保存的时候自动修复编码
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.fixAll.markdownlint": "explicit"
    },
    "eslint.run": "onSave",
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[vue]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": true
    }
}
