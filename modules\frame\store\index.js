const state = {
    isLogin: false,
    hasMenu: false
};

const mutations = {
    SET_ISLOGIN: (state, flag) => {
        state.isLogin = flag;
    },
    SET_HASMENU: (state, flag) => {
        state.hasMenu = flag;
    }
};

const actions = {
    toggleDevice({ commit }, flag) {
        commit('SET_ISLOGIN', flag);
    },
    saveMenuState({ commit }, flag) {
        commit('SET_HASMENU', flag);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
