<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-checkbox-group v-model="config.modelObj[config.modelKey]" v-bind="elCheckboxAttrs" @change="handleChange">
            <el-checkbox
                v-for="(checkbox, index) in config.elCheckboxes"
                :key="index"
                :label="checkbox.value"
                :disabled="checkbox.disabled"
            >
                {{ checkbox.label }}
            </el-checkbox>
        </el-checkbox-group>
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormCheckbox',
    props: {
        /**
         * SnbcFormCheckbox组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elCheckboxes: [],
                    elFormItemAttrs: {},
                    elCheckboxAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-checkbox-group组件默认属性设置
            defaultElCheckboxAttrs: {
                size: 'small'
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-checkbox-group组件应用属性
        elCheckboxAttrs() {
            return {
                ...this.defaultElCheckboxAttrs,
                ...(this.config.elCheckboxAttrs || {})
            };
        }
    },
    methods: {
        // 复选框数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
