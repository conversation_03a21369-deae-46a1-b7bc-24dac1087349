<template>
    <div class="error-container">
        <div class="error-context">
            <img src="../../assets/error-img/error-500.png" alt="">
            <div class="message">
                <div class="tip">{{ $t('frame.msg.errorPage') }}</div>
                <el-button type="primary" class="message-button" @click="backHandler">{{ $t('frame.goBack') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: '500',
    methods: {
        backHandler() {
            this.$router.push('/'); // 跳回首页
        }
    }
};
</script>

<style lang="scss" scoped>
	.error-container{
		width:100%;
		height:100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.error-context{
			img{
				width:411px;
				height:243px;
			}
			.message{
				text-align: center;
				margin-top:40px;
				.tip{
					margin-top:15px;
					margin-bottom:30px;
				}
				.message-button{
					border-radius: 8px;
					height:40px;
				}
			}
		}
	}
</style>
