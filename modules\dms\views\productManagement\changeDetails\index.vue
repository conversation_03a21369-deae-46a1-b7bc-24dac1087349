<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="confirm('已拒绝')" type="danger" v-if="type === '审核'">拒绝</el-button>
            <el-button @click="confirm('已通过')" type="primary" v-if="type === '审核'">批准</el-button>
            <el-button @click="cancel">返回</el-button>
        </div>
        <div>
            <!-- 产品结构 -->
            <formula-title :title="basicTitle"></formula-title>
            <!-- 审批意见 -->
            <formula-title :title="checkTitle" v-if="type === '审核'"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="80px" v-if="type === '审核'">
                <el-form-item label="审批意见" prop="checkOpinion">
                    <el-input
                        type="textarea"
                        v-model="addForm.checkOpinion"
                        maxlength="500"
                        :rows="4"
                        placeholder="请填写您的审批意见"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';

export default {
    components: { formulaTitle },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '产品结构',
            checkTitle: '审批意见',
            type: '',
            id: '',
            addForm: {
                checkOpinion: ''
            }
        };
    },
    mounted() {
        this.type = this.$route.query.type;
        this.id = this.$route.query.id;
    },
    methods: {
        async confirm(val) {
            try {
                const params = this.addForm;
                params.checkId = this.id;
                params.checkResult = val;
                if (val === '已拒绝') {
                    if (this.addForm.checkOpinion === '' || this.addForm.checkOpinion === null) {
                        this.$message.error('请填写您的审批意见');
                        return;
                    }
                }
                // 4. 调用后端接口提交数据
                const res = await this.$service.dms.product.productCheck(params);
                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.$router.back();
                } else {
                    this.$message.error(res.message || '审核失败，请重试');
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        // 关闭弹窗
        cancel() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped></style>
