/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import httpErrorHandle from '../common/httpErrorHandle.js';
import httpInterceptors from '../common/httpInterceptors';
import { basePathInit } from '../../../src/envConst';

export default (Vue) => {
    const http = Vue.prototype.$http;
    // 进行请求拦截处理
    httpInterceptors(Vue, http);
    // 响应结果处理
    http.responseHandle(httpErrorHandle);
    // 根服务对象
    const basePath = basePathInit();

    const service = {
        /**
         * 获取左侧菜单列表
         *
         * @param {*} data 参数
         * @returns {Promise} promise
         */
        getMenuList(data) {
            return http({
                baseDomain: basePath.systemApi.system,
                url: '/authority/getPermissionTree',
                method: 'get',
                params: data
            });
        },
        /**
         * 功能导航菜单
         *
         * @param {*} data 参数
         * @returns {Promise} promise
         */
        getFunctionMenu(data) {
            return http({
                baseDomain: basePath.systemApi.system,
                url: '/authority/queryPermissionMnueGroupByUserId',
                method: 'get',
                params: data
            });
        }
    };

    return service;
};
