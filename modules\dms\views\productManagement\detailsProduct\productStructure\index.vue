<template>
    <div class="view-box">
        <DetailTree :data="projectData"></DetailTree>
    </div>
</template>

<script>
import DetailTree from 'dms/views/productManagement/components/DetailTree.vue';

export default {
    components: {
        DetailTree
    },
    props: {
        id: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            projectData: {}
        };
    },
    created() {
        if (this.id) {
            this.getModuleInfo();
        }
    },
    methods: {
        async getModuleInfo() {
            try {
                const params = {
                    productId: this.id
                };
                const res = await this.$service.dms.product.getProductModuleInfo(params);
                if (res.code === '0000') {
                    this.projectData = res.data;
                    console.log(this.projectData, 'projectData');
                } else {
                    this.$message.error(res.message || '获取产品详情失败');
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
