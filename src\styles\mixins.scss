/** 全局媒体查询边界点
    使用示例：
    .example {
      font-size: 16px;

      @include respond-to('md') {
        font-size: 14px;
      }

      @include respond-to('xs') {
        font-size: 12px;
      }
    }
*/
$breakpoints: (
    'xs': 575.98px,
    'sm': 767.98px,
    'md': 991.98px,
    'lg': 1199.98px,
    'xl': 1599.98px
);

@mixin respond-to($breakpoint) {
    $breakpointValue: map-get($breakpoints, $breakpoint);

    @media (max-width: $breakpointValue) {
        @content;
    }
}
