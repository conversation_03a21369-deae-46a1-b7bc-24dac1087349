import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange, numberRange, peopleSelector, productLineSelector, number } = CommonItems;

// 部门
const department = {
    ...select,
    name: '部门',
    modelKey: 'department'
};

// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};

// 团队名称
const productName = {
    ...input,
    name: '团队名称',
    modelKey: 'productName'
};

// Team Leader
const productNumber = {
    ...input,
    name: 'Team Leader',
    modelKey: 'productNumber'
};

// 直属上级
const productOwner = {
    ...input,
    name: '直属上级',
    modelKey: 'productOwner'
};

// 负责产品
const productId = {
    ...input,
    name: '负责产品',
    modelKey: 'productId'
};

// 创建时间
const productStatus12 = {
    ...dateRange,
    name: '创建时间',
    modelKey: 'productStatus'
};

// 关闭时间
const productStatus = {
    ...dateRange,
    name: '关闭时间',
    modelKey: 'productStatus'
};

// 团队级别
const productLine1 = {
    ...select,
    name: '团队级别',
    modelKey: 'productLine'
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [productLine, productName, productNumber, productOwner, productId, productStatus]
};

// 查询条件参数
export const queryParams = {
    productLine: '',
    productName: '',
    productNumber: '',
    productOwner: '',
    productId: '',
    productStatus: '',
    foundDate: [],
    closeDate: []
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'productStatus' },
    { field: '进行中', name: '进行中', queryField: 'productStatus' },
    { field: '关闭', name: '关闭', queryField: 'productStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productId',
        label: '部门',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productNumber',
        label: '直属上级',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '团队名称',
        width: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwner',
        label: 'Team Leader',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'foundDate',
        label: '团队级别',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'productStatus',
        label: '负责产品',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'showMoney',
        label: '团队状态',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'closeDate',
        label: '负责产品',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'actions1',
        label: '团队状态',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'actions2',
        label: '创建时间',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'actions3',
        label: '关闭时间',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    }
];
