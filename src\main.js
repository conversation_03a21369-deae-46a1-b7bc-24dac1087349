import Vue from 'vue';

import WtfCoreVue from 'wtf-core-vue-ng';
import settings from './settings.js';
import VueCookies from 'vue-cookies';

import { expandTools } from './tools.js';
import directives from '../modules/frame/directives/index';

import './styles/wtf-variables.scss';

import moment from 'moment';
// 导入文件

import './global.scss';
// 需要汉化
import ElementUI from 'element-ui';

expandTools();

Vue.use(VueCookies);
Vue.use(directives);
// 赋值使用
Vue.prototype.$moment = moment;
moment.locale('zh-cn');

// 导入所有项目模块入口文件
const modulesFiles = require.context('../modules', true, /module_index.js$/);
// 引入BASE64解码
const { Base64 } = require('js-base64');
// 存储所有模块对象
const modules = modulesFiles.keys().reduce((module, modulePath) => {
    // set './app.js' => 'app'
    const moduleName = modulePath.replace(/^\.\/(.*)\/module_index.\w+$/, '$1');
    const value = modulesFiles(modulePath);
    module[moduleName] = value.default;
    return module;
}, {});

// 获得模块排序数组
const { modulesSortList } = settings;
if (modulesSortList && modulesSortList.length > 0) {
    // 如果配置了模块排序数组，则按照模块排序数组加载
    for (let i = 0; i < modulesSortList.length; i++) {
        if (modules[modulesSortList[i]]) {
            WtfCoreVue.add(modules[modulesSortList[i]]);
        }
    }
} else {
    // 未配置模块排序数组，则把所有模块对象都进行加载
    for (const moduleName in modules) {
        if (Object.hasOwnProperty.call(modules, moduleName)) {
            WtfCoreVue.add(modules[moduleName]);
        }
    }
}

// ====设置element组件全局默认属性值===============
// 弹窗组件--设置点击背景不允许关闭
ElementUI.Dialog.props.closeOnClickModal.default = false;
ElementUI.Tooltip.props.openDelay.default = 300;
ElementUI.Tooltip.props.transition.default = '';
// 弹窗组件--设置不允许滚动(否则出现滚动条时会闪烁)
ElementUI.Dialog.props.lockScroll.default = false;

// 创建实例
const appElement = document.createElement('div');
const app = new WtfCoreVue({
    settings,
    Base64
});
// 从cookie中更新用户信息
app.$store.dispatch('user/setUserName', VueCookies.get('username'));
// 挂载实例时更新size
Vue.prototype.$ELEMENT.size = 'mini';
app.$mount(appElement);
document.body.appendChild(app.$el);
