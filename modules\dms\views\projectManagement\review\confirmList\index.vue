<template>
    <div class="project-management-list">
        <el-table class="dms-table" :data="productData" style="width: 100%">
            <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.prop"
                :label="column.label"
                :width="column.width"
                header-align="center"
            >
            </el-table-column>
            <el-table-column label="操作" width="100" header-align="center" fixed="right">
                <template slot-scope="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <el-button type="text" size="small" @click="handleConfirm(row)">确认</el-button>
                </template>
            </el-table-column>
        </el-table>
        <FormalProjectDetailDialog
            :visible.sync="formalProjectDetailDialogVisible"
            :projectId="currentProjectId"
        ></FormalProjectDetailDialog>
        <ProjectConfirmDialog
            :visible.sync="projectConfirmDialogVisible"
            :projectId="currentProjectId"
        ></ProjectConfirmDialog>
    </div>
</template>

<script>
import { productColumns } from './config.js';
import FormalProjectDetailDialog from 'dms/views/projectManagement/projectList/components/FormalProjectDetailDialog.vue';
import ProjectConfirmDialog from './ProjectConfirmDialog.vue';

export default {
    name: 'ProjectManagementList',
    components: { FormalProjectDetailDialog, ProjectConfirmDialog },
    data() {
        return {
            // 表格列配:置
            columns: productColumns,
            // 产品数据
            // TODO: 改回去
            // productData: [],
            productData: [{}, {}, {}, {}, {}],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10,
            // 当前选中的项目ID
            currentProjectId: null,
            // 查看正式项目详情弹窗
            formalProjectDetailDialogVisible: false,
            // 项目确认弹窗
            projectConfirmDialogVisible: false
        };
    },
    created() {
        this.query();
    },
    methods: {
        // 加载产品数据
        async query() {
            return;
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams
                };

                // 日期处理
                if (params.foundDate && params.foundDate.length > 0) {
                    params.startDateString = params.foundDate[0];
                    params.endDateString = params.foundDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closeStartDateString = params.closeDate[0];
                    params.closeEndDateString = params.closeDate[1];
                }

                const response = await this.$service.dms.common.getProjectOrGroupList(params);

                if (response.code === '000000') {
                    this.productData = response.result?.list || [];
                    this.total = response.result?.total || 0;
                } else {
                    this.$message.error(response.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 编辑产品
        handleEdit(row) {},

        // 查看正式项目详情
        handleView(row) {
            this.currentProjectId = row.projectId;
            this.formalProjectDetailDialogVisible = true;
        },
        handleConfirm(row) {
            this.currentProjectId = row.projectId;
            this.projectConfirmDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
