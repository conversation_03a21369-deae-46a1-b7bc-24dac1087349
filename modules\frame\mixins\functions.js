// 获取BBPF1.5 菜单列表
export default {
    data() {
        return {};
    },
    computed: {},
    mounted() {
        // 通过tag切换打开后，将store的记录移除，该情况下页面参数可能特殊处理
        this.$store.commit('tagsView/TOGGLE_VIEW', null);
    },
    methods: {
        /**
         * 功能权限检查
         *
         * @param {String} permissionCode 权限编码
         * @returns {Boolean} 校验结果
         */
        checkPermission(permissionCode) {
            return this.$store.state.permission.btnDatas.includes(permissionCode);
        }
    }
};
