<template>
    <div class="icons-container">
        <div class="grid">
            <div v-for="item of dataSource" :key="item.className" @click="clickHandler(item,$event)" @dblclick="dbclickHandler(item)">
                <div class="icon-item" :class="{'icon-selected': item.isChecked}">
                    <i v-if="item.isElementIcon" :class="item.className" />
                    <svg-icon v-else :icon-class="item.className" class-name="disabled" />
                    <span>{{ item.className }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { initData, getIconItem } from './icons';

export default {
    name: 'Icons',
    props: {
        selectedIconName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dataSource: [],
            selectedIcon: null
        };
    },
    watch: {
        selectedIconName: {
            immediate: true, // 首次加载的时候执行函数
            handler(val) {
                this.showHandler();
            }
        }
    },
    created() {
        // 初始化矢量图标数据源
        this.dataSource = initData();
        // 显示逻辑
        this.showHandler();
    },
    methods: {
        // 重置选中的icon
        resetSelectedIcon() {
            this.selectedIcon = {
                iconName: '',
                className: '',
                isChecked: false,
                isElementIcon: true
            };
        },
        // 初始化显示逻辑，如果有选中的要回显
        showHandler() {
            if (this.selectedIcon && this.selectedIcon.isChecked) {
                this.selectedIcon.isChecked = false;
            }
            if (this.selectedIconName) {
                for (let index = 0; index < this.dataSource.length; index++) {
                    const element = this.dataSource[index];
                    if (element.className === this.selectedIconName) {
                        element.isChecked = true;
                        this.selectedIcon = element;
                        break;
                    }
                }
            } else {
                this.resetSelectedIcon();
            }
        },
        // 单击事件处理函数
        clickHandler(item, event) {
            if (item.isChecked) {
                item.isChecked = false;
                this.resetSelectedIcon();
            } else {
                if (this.selectedIcon) {
                    this.selectedIcon.isChecked = false;
                }
                item.isChecked = true;
                this.selectedIcon = item;
            }
            // 此处必须调用getIconItem深copy传递出去，避免业务页面清除导致的bug
            this.$emit('iconSelectedEventByClick', getIconItem(this.selectedIcon.className));
        },
        // 双击事件处理函数
        dbclickHandler(item, event) {
            if (item.isChecked) {
                item.isChecked = false;
                this.resetSelectedIcon();
            } else {
                if (this.selectedIcon) {
                    this.selectedIcon.isChecked = false;
                }
                item.isChecked = true;
                this.selectedIcon = item;
            }
            // 此处必须调用getIconItem深copy传递出去，避免业务页面清除导致的bug
            this.$emit('iconSelectedEventByDBClick', getIconItem(this.selectedIcon.className));
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/global.scss";
.icons-container {
    width: 100%;
    height: 60vh;
    overflow: auto;

    .icon-selected {
        color: $theme-color !important;
    }

    .grid {
        position: relative;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .icon-item {
        margin: 20px;
        height: 85px;
        text-align: center;
        width: 100px;
        float: left;
        font-size: 30px;
        color: #24292e;
        cursor: pointer;
    }

    .icon-item:hover {
        color: $theme-color;
    }

    span {
        display: block;
        font-size: 16px;
        margin-top: 10px;
    }

    .disabled {
        pointer-events: none;
    }
}
</style>
