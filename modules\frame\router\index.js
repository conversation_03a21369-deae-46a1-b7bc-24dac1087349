export default [
    {
        path: '/redirect',
        useLayout: true,
        hidden: true,
        noPermission: true,
        name: 'redirect',
        children: [
            {
                path: '/redirect/:path(.*)',
                name: 'redirectIndex',
                hidden: true,
                noPermission: true,
                component: () => import('../views/redirect/index')
            }
        ]
    },
    {
        path: '/app',
        name: 'dashboard',
        noPermission: true,
        useLayout: true,
        redirect: 'dashboard-index',
        children: [
            {
                path: 'dashboard-index',
                component: () => import('../views/dashboard/index'),
                name: 'DashboardIndex',
                noPermission: true,
                meta: { title: 'dashboard', icon: 'fa fa-home', affix: true }
            }
        ]
    },
    {
        path: '/401',
        name: '401',
        noPermission: true,
        component: () => import('../views/error-page/401'),
        hidden: true,
        meta: { title: '401', icon: 'el-icon-key' }
    },
    {
        path: '/403',
        name: '403',
        noPermission: true,
        component: () => import('../views/error-page/403'),
        hidden: true,
        meta: { title: '403', icon: 'el-icon-key' }
    },
    {
        path: '/404',
        name: '404',
        noPermission: true,
        component: () => import('../views/error-page/404'),
        hidden: true,
        meta: { title: '404', icon: 'el-icon-key' }
    },
    {
        path: '/500',
        name: '500',
        noPermission: true,
        component: () => import('../views/error-page/500'),
        hidden: true,
        meta: { title: '500', icon: 'el-icon-key' }
    },
    {
        path: '/error',
        name: 'error',
        noPermission: true,
        hidden: true,
        useLayout: true,
        meta: { title: 'error', icon: 'el-icon-key' },
        children: [
            {
                path: '401',
                name: 'error-401',
                noPermission: true,
                component: () => import('../views/error-page/401'),
                hidden: true,
                meta: { title: '401', icon: 'el-icon-key' }
            },
            {
                path: '403',
                name: 'error-403',
                noPermission: true,
                component: () => import('../views/error-page/403'),
                hidden: true,
                meta: { title: '403', icon: 'el-icon-key' }
            },
            {
                path: '404',
                name: 'error-404',
                noPermission: true,
                component: () => import('../views/error-page/404'),
                hidden: true,
                meta: { title: '404', icon: 'el-icon-key' }
            },
            {
                path: '500',
                name: 'error-500',
                noPermission: true,
                component: () => import('../views/error-page/500'),
                hidden: true,
                meta: { title: '500', icon: 'el-icon-key' }
            }
        ]
    }
];
