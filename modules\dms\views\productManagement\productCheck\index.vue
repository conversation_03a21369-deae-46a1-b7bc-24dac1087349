<template>
    <div class="view">
        <project-list
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :columns="columns"
            :data="productData"
            :total="total"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @pagination="handlePagination"
            :table-attrs="{ height: 'calc(100vh - 230px)' }"
        >
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                <el-button type="text" size="small" @click="handleAudit(row)">审核</el-button>
            </template>
        </project-list>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';

export default {
    name: 'ProductCheck',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '所有',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            currentPage: 1,
            pageSize: 10
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    type: '审核'
                };
                if (this.activeNavTab === '所有') {
                    params.status = '';
                } else {
                    params.status = this.activeNavTab;
                }
                // 日期处理
                if (params.applyDate && params.applyDate.length > 0) {
                    params.createStartDate = params.applyDate[0];
                    params.createEndDate = params.applyDate[1];
                }
                if (params.checkDate && params.checkDate.length > 0) {
                    params.closedStartDate = params.checkDate[0];
                    params.closedEndDate = params.checkDate[1];
                }
                const res = await this.$service.dms.product.getProductCheckList(params);
                if (res.code === '0000') {
                    this.productData = res.data.list || [];
                    this.total = res.data.total || 0;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },
        // 处理搜索
        handleSearch() {
            this.currentPage = 1;
            this.loadProductData();
        },
        // 处理重置
        handleReset() {
            this.currentPage = 1;
            this.loadProductData();
        },
        // 处理导航切换
        handleNavChange() {
            this.loadProductData();
        },
        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },
        // 查看详情
        handleDetails(row) {
            this.$router.push({ name: 'ChangeDetails', query: { type: '详情', id: row.productId } });
        },

        // 审核产品
        handleAudit(row) {
            this.$router.push({ name: 'ChangeDetails', query: { type: '审核', id: row.productId } });
        }
    }
};
</script>

<style lang="scss" scoped></style>
