<template>
    <div class="org-chart-container">
        <!-- 团队页面，同时团队没有对应的小组ID，才可以关联小组-->
        <el-button class="relate-button" type="primary" v-if="isShowRelateButton" @click="handleRelateGroup"
            >关联小组</el-button
        >
        <!-- 项目页面，同时团队没有对应的小组ID，才可以关联小组-->
        <el-button class="add-group-button" type="primary" v-if="isShowAddGroupButton" @click="handleAddGroup"
            >新建小组</el-button
        >
        <el-empty :image-size="50" v-if="computedHideChart"></el-empty>
        <div v-else>
            <el-radio-group
                v-if="!isGroupOrProject"
                v-model="departmentType"
                class="department-type-button"
                @input="getOrgChartData"
            >
                <el-radio-button label="业务">业务线资源人力</el-radio-button>
                <el-radio-button label="部门">本部门资源人力</el-radio-button>
            </el-radio-group>
            <OrgChart
                :datasource.sync="dataSource"
                class="project-org-chart"
                @node-add="handleAdd"
                @node-delete="handleDelete"
                @node-edit="handleEdit"
            >
                <template #prefix
                    ><div class="project-org-legend">
                        <div class="info"><i class="fa fa-solid fa-user manager"></i>项目经理</div>
                        <div class="info"><i class="fa fa-solid fa-user pqa"></i>工程师</div>
                        <template v-if="!isProject">
                            <div class="info">
                                <div class="multi-team-member"></div>
                                <span>多团队成员</span>
                            </div>
                            <div class="info">
                                <div class="other-department-member"></div>
                                <span>其他部门成员</span>
                            </div>
                        </template>
                    </div>
                </template>
            </OrgChart>
            <OrgAddDialog
                :visible.sync="addDialogVisible"
                :type="type"
                :currentNodeData="currentNodeData"
                :org-type="orgType"
                @success="updateChartAndTopList"
            />
        </div>
        <OrgRelateDialog :visible.sync="relateDialogVisible" @success="realteTeamSuccess" :objectInfo="objectInfo" />
    </div>
</template>
<script>
import OrgChart from 'dms/components/OrgChart/OrgChart.vue';
import OrgAddDialog from './OrgAddDialog.vue';
import OrgRelateDialog from './OrgRelateDialog.vue';
import { generateTreeData } from './treeConverter';

export default {
    name: 'ProjectOrgChart',
    components: { OrgChart, OrgAddDialog, OrgRelateDialog },
    props: {
        activeName: {
            type: String,
            default: ''
        },
        // 是否是团队页面
        isGroup: {
            type: Boolean,
            default: false
        },
        // 是否是项目页面
        isProject: {
            type: Boolean,
            default: false
        },
        // 由外部控制是否展示组织结构图
        showOrgChart: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dataSource: {},
            addDialogVisible: false,
            relateDialogVisible: false,
            type: 'add',
            currentNodeData: {},
            orgType: 'department',
            // 业务线资源人力/本门线资源人力，切换按钮会展示不同的小组
            departmentType: '业务'
        };
    },
    computed: {
        // 兼容原有逻辑的计算属性
        isGroupOrProject() {
            return this.isGroup || this.isProject;
        },
        department() {
            return this.$store.state.dms.department;
        },
        departmentOption() {
            return this.$store.state.dms.departmentOption;
        },
        // 项目下拉框选项
        projectOption() {
            return this.$store.state.dms.projectOption;
        },
        // 是否隐藏组织图
        computedHideChart() {
            const isEmpty = Object.keys(this.dataSource).length === 0;
            if (isEmpty) return true;
            return !this.showOrgChart;
        },
        // 项目ID或者是团队ID信息
        objectInfo() {
            if (this.isProject) {
                return {
                    objectId: this.$store.state.dms.project,
                    objectType: '项目'
                };
            }
            if (this.isGroup) {
                return {
                    objectId: this.$store.state.dms.group,
                    objectType: '团队'
                };
            }
            return {};
        },
        // 是否展示关联小组按钮，团队页面，有团队ID，但是没有小组ID（datasource为空），就展示
        isShowRelateButton() {
            const isEmpty = Object.keys(this.dataSource).length === 0;
            const hasId = Object.keys(this.objectInfo).length !== 0 && this.objectInfo?.objectId;
            return this.isGroup && hasId && isEmpty;
        },
        // 是否展示新建小组按钮，项目页面，有项目ID，但是没有小组ID（datasource为空），就展示
        isShowAddGroupButton() {
            const isEmpty = Object.keys(this.dataSource).length === 0;
            const hasId = Object.keys(this.objectInfo).length !== 0 && this.objectInfo?.objectId;
            return this.isProject && hasId && isEmpty;
        }
    },
    watch: {
        department(newVal) {
            if (newVal) {
                this.getOrgChartData();
            }
        },
        activeName() {
            this.getOrgChartData();
        }
    },
    mounted() {
        this.getOrgChartData();
    },
    methods: {
        async updateChartAndTopList() {
            // 必须等待图表查询完毕之后更新页头，否则不会更新
            await this.getOrgChartData();
            this.$nextTick(() => {
                this.$emit('updateTopList');
            });
        },
        /**
         * 关联完小组之后
         */
        realteTeamSuccess() {
            this.$emit('relate-team-success');
        },
        /**
         * 关联小组（根据项目ID或是团队ID）
         */
        handleRelateGroup() {
            this.relateDialogVisible = true;
        },
        /**
         * 添加节点
         * @param  {Object} nodeData 节点数据
         */
        handleAdd(nodeData) {
            if (nodeData.title.includes('部门')) {
                this.orgType = 'department';
            } else {
                this.orgType = 'team';
            }
            this.currentNodeData = nodeData;
            this.type = 'add';
            this.addDialogVisible = true;
        },
        /**
         * 编辑节点
         * @param  {Object} nodeData 节点数据
         */
        handleEdit(nodeData) {
            if (nodeData.title.includes('部门')) {
                this.orgType = 'department';
            } else {
                this.orgType = 'team';
            }
            this.type = 'edit';
            this.currentNodeData = nodeData;
            this.addDialogVisible = true;
        },
        /**
         * 删除节点
         * @param  {Object} nodeData 节点数据
         */
        async handleDelete(nodeData) {
            let params = {};
            let api = '';
            if (nodeData.title.includes('项目经理')) {
                await this.$tools.confirm('确定移除该团队吗?');
                params = {
                    teamId: nodeData.id,
                    orgCode: nodeData.belongOrgCode
                };
                api = this.$service.dms.group.removeGroup;
            } else {
                await this.$tools.confirm('确定移除该成员吗?');
                params = {
                    memberId: nodeData.id,
                    teamId: nodeData.belongOrgCode
                };
                api = this.$service.dms.group.removeGroupMember;
            }
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('移除成功');
                // 必须等待图表查询完毕之后更新页头，否则不会更新
                await this.getOrgChartData();
                this.$nextTick(() => {
                    // 移除了团队之后，要更新顶部级联选择框的选项
                    if (nodeData.title.includes('项目经理')) {
                        this.$emit('updateTopList');
                    }
                });
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取组织结构图数据
         */
        async getOrgChartData() {
            if (!this.activeName.includes('OrgChart')) {
                this.dataSource = {};
                return;
            }
            if (this.department.length === 0) {
                this.dataSource = {};
                return;
            }
            // 没有查出任何小组或部门，直接返回
            if (Object.values(this.department).every((i) => i === '' || i === null)) {
                this.dataSource = {};
                return;
            }
            const api = this.$service.dms.group.getMembers;
            let orgCode = '';
            if (this.department[1]) {
                orgCode = this.department[1];
            } else {
                orgCode = this.department[0] || '';
            }
            // 判断类型
            let teamType = null;
            if (this.isGroup) {
                teamType = '部门';
            } else if (this.isProject) {
                teamType = '项目';
            }
            const params = {
                orgCode,
                orgLevel: this.department.length,
                teamId: this.department[2] || '',
                type: this.isGroupOrProject ? '业务' : this.departmentType,
                teamType
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.dataSource = generateTreeData(
                    res.data,
                    this.department,
                    this.departmentOption,
                    this.isGroupOrProject
                );
                Promise.resolve();
            } catch (error) {
                console.error('Error:', error);
                Promise.reject();
            }
        },
        /**
         * 新增小组
         */
        async handleAddGroup() {
            const { pm, projectName, projectId } = this.projectOption;
            const params = { teamName: projectName, projectId, leaderAccount: pm };
            const api = this.$service.dms.group.addTeamWithoutDepartment;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('新建成功');
                // 这里重新赋值小组id
                this.$store.dispatch('dms/setDepartment', ['', '', res.data]);
                this.$store.dispatch('dms/setDepartmentOption', []);
                this.getOrgChartData();
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.org-chart-container {
    position: relative;
}
.add-group-button,
.relate-button {
    position: absolute;
    right: 0px;
    top: 10px;
    z-index: 1;
}
.department-type-button {
    position: absolute;
    right: 35px;
    top: 25px;
    z-index: 1;
}
.project-org-chart {
    padding-top: 20px;
    margin-top: 15px;
    height: calc(100vh - 230px);
    .project-org-legend {
        position: absolute;
        top: 10px;
        left: 20px;
        display: flex;
        width: calc(100% - 30px);
        .department-type-button {
            position: absolute;
            right: 0;
            top: 0;
        }
        .info {
            margin-right: 15px;
            display: flex;
            align-items: center;
        }
        .manager {
            margin-right: 5px;
            color: #4169e1;
        }
        .pqa {
            margin-right: 5px;
            color: #32cd32;
        }
        .multi-team-member {
            width: 10px;
            height: 10px;
            background-color: #8b3e86;
            border-radius: 50%;
            margin-right: 5px;
        }
        .other-department-member {
            width: 10px;
            height: 10px;
            background-color: #ff7f00;
            border-radius: 50%;
            margin-right: 5px;
        }
    }
}
</style>
