<template>
    <el-cascader
        class="selector"
        @change="handleChange"
        :placeholder="placeholder"
        :props="props"
        v-model="currentValue"
        :options="options"
        ref="cascaderRef"
        popper-class="OrgDepartmentSelector-cascader"
        filterable
    >
        <div slot-scope="{ data }" @click="clickNode" class="span-click" :class="data.disabled ? 'is-disabled' : ''">
            {{ data.orgName }}
        </div>
    </el-cascader>
</template>
<script>
/**
 * OrgDepartmentSelector.vue 组件
 * @desc 该组件用于展示信息的级联选择
 * @param {String} [placeholder]   - 级联选择器的占位符文本
 * @param {String} [year]   - 项目经理与年份有关，所以有这个配置
 * @param {Array} [value] - 级联选择器当前选择的值，数组格式, 支持v-model
 * @example 调用示例
 *  <OrgDepartmentSelector.vue :placeholder="placeholder" v-model="department"></OrgDepartmentSelector.vue>
 * */

export default {
    name: 'OrgDepartmentSelector',
    props: {
        value: {
            type: Array,
            default: () => []
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        year: {
            type: String,
            default: String(new Date().getFullYear())
        }
    },
    data() {
        return {
            props: {
                // 是否可以直接选中父节点
                checkStrictly: true,
                // 配置展开方式
                expandTrigger: 'hover',
                // 悬停状态保持时间，小于这个时间不会触发hover事件
                hoverThreshold: 150,
                value: 'orgCode',
                label: 'orgName'
            },
            options: [],
            // 当前项目状态
            currentStatus: ''
        };
    },
    computed: {
        currentValue: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        }
    },
    mounted() {
        this.getOptions();
        // 挂载后向外传递ref
        this.$nextTick(() => {
            this.$emit('cascaderRef', this.$refs.cascaderRef);
        });
    },
    activated() {
        this.getOptions();
    },
    methods: {
        /**
         * 通过点击文字选中的处理函数
         * @param {Object} e 事件对象
         */
        clickNode(e) {
            // 模拟点击对应的radio
            e.target.parentElement.parentElement.firstElementChild.click();
        },
        /**
         * 选择的值发生变化时触发
         * @param {Array} value 选择的值
         */
        handleChange() {
            const checkedNodes = this.$refs.cascaderRef.getCheckedNodes();
            if (checkedNodes.length > 0) {
                const nodePathData = checkedNodes[0]?.pathNodes?.map((node) => node.data);
                this.$emit('data-change', nodePathData);
            }

            // 每次选择结束之后自动关闭
            if (this.$refs?.cascaderRef?.dropDownVisible) {
                this.$refs.cascaderRef.dropDownVisible = false;
            }
        },
        /**
         * 获取选择项
         * 每次选择项变更之后都会进行一次查询
         */
        async getOptions() {
            const api = this.$service.dms.common.getOrgList;
            const params = {
                orgCode: '0002'
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.options = this.handleOptions(res.data);
            } catch (error) {
                console.error(error);
            }
        },
        handleOptions(data) {
            return data.map((i) => ({
                ...i,
                disabled: true,
                children: i.children.map((j) => ({
                    ...j,
                    disabled: true,
                    children: j.children.map((k) => ({
                        orgCode: k.teamId,
                        orgName: k.teamName
                    }))
                }))
            }));
        }
    }
};
</script>

<style lang="scss" scoped>
.selector {
    margin: 0 10px 0 10px;
    width: 100%;
    ::v-deep .el-input--mini .el-input__inner {
        line-height: 34px;
        height: 34px;
        font-weight: 400;
    }
}
// 利用placeholder进行数据回显，修改字体颜色
::v-deep .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685) !important;
}
.span-click {
    width: 100%;
}
.span-click.is-disabled {
    color: rgb(192, 196, 204);
}
</style>
<style lang="scss">
// 隐藏单选框
.OrgDepartmentSelector-cascader .el-cascader-panel .el-radio__input {
    display: none;
}
.OrgDepartmentSelector-cascader .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
}
.OrgDepartmentSelector-cascader .el-cascader-menu__wrap {
    height: 300px;
}
</style>
