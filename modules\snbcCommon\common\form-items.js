const input = {
    name: '单行输入',
    component: 'SnbcFormInput',
    modelKey: 'input'
};
const number = {
    name: '数值输入',
    component: 'SnbcFormInputNumber',
    modelKey: 'number'
};
const numberRange = {
    name: '数值区间',
    component: 'SnbcFormInputNumberRange',
    modelKey: 'numberRange'
};
const textarea = {
    name: '多行输入',
    component: 'SnbcFormTextarea',
    modelKey: 'textarea'
};
const select = {
    name: '单选下拉',
    component: 'SnbcFormSelect',
    modelKey: 'select',
    elOptions: []
};
const radio = {
    name: '单选',
    component: 'SnbcFormRadio',
    modelKey: 'radio',
    elRadios: []
};
const checkbox = {
    name: '多选',
    component: 'SnbcFormCheckbox',
    modelKey: 'checkbox',
    elCheckboxes: []
};
const region = {
    name: '省市区',
    component: 'SnbcFormRegion',
    modelKey: 'region',
    type: 'area'
};

const cityRange = {
    name: '省市',
    component: 'SnbcFormMultiCitySelect',
    modelKey: 'cityRange',
    type: 'city'
};
const monthRange = {
    name: '开始日期',
    component: 'SnbcFormDateMonthPicker',
    modelKey: 'dateRange'
};

const date = {
    name: '日期',
    component: 'SnbcFormDatePicker',
    modelKey: 'date'
};
const dateRange = {
    name: '起止日期',
    component: 'SnbcFormDateRangePicker',
    modelKey: 'dateRange'
};
const file = {
    name: '文件上传',
    component: 'SnbcFormFileUpload',
    modelKey: 'fileList',
    elUploadAttrs: {
        action: ''
    }
};

const site = {
    name: '日期',
    component: 'SnbcFormSiteSelect',
    modelKey: 'siteCode'
};

const peopleSelector = {
    name: '人员选择',
    component: 'SnbcFormPeopleSelector',
    modelKey: 'loginName'
};

const productLineSelector = {
    name: '产品线',
    component: 'SnbcFormProductLineSelector',
    modelKey: 'productLine'
};

const departmentSelector = {
    name: '部门',
    component: 'SnbcFormDepartmentSelector',
    modelKey: 'department'
};

export default {
    input,
    number,
    numberRange,
    textarea,
    select,
    radio,
    checkbox,
    region,
    date,
    dateRange,
    file,
    cityRange,
    site,
    peopleSelector,
    productLineSelector,
    departmentSelector,
    monthRange
};
