import svgIcons from './svg-icons';
import elementIcons from './element-icons';
import fontawesomeIcons from './font-awesome-icons';

let iconsArray = [];

/**
  * 初始化矢量图数组，并格式化成绑定对象
 */
const initData = () => {
    const tempArray = [];
    elementIcons.forEach((item) => {
        const temp = {
            iconName: item,
            className: `el-icon-${item}`,
            isChecked: false,
            isElementIcon: true
        };
        tempArray.push(temp);
    });
    fontawesomeIcons.forEach((item) => {
        const temp = {
            iconName: item,
            className: `fa fa-${item}`,
            isChecked: false,
            isElementIcon: true
        };
        tempArray.push(temp);
    });
    svgIcons.forEach((item) => {
        const temp = {
            iconName: item,
            className: item,
            isChecked: false,
            isElementIcon: false
        };
        tempArray.push(temp);
    });
    iconsArray = [].concat(tempArray);
    return iconsArray;
};

/**
  * 根据样式名称获得矢量图对象
 */
const getIconItem = (itemName) => {
    if (!itemName) {
        return null;
    }
    if (iconsArray.length === 0) {
        initData();
    }
    let iconItem = null;
    for (let index = 0; index < iconsArray.length; index++) {
        const element = iconsArray[index];
        if (element.className === itemName) {
            // 这里需要深copy，防止清除对象后这里变null了
            iconItem = {
                iconName: element.iconName,
                className: element.className,
                isChecked: element.isChecked,
                isElementIcon: element.isElementIcon
            };
            break;
        }
    }
    return iconItem;
};

export {
    initData,
    getIconItem
};
