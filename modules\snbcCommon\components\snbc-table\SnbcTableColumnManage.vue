<template>
    <div class="column-manager">
        <el-popover
            ref="columnPopover"
            trigger="click"
            placement="left"
            :append-to-body="appendToBody"
            popper-class="column-manager-popover"
        >
            <div class="column-manager-box">
                <div class="grid-table">
                    <!-- 表头 -->
                    <div class="grid-header">
                        <div class="grid-cell header-cell">列名称</div>
                        <div class="grid-cell header-cell">显示</div>
                        <div class="grid-cell header-cell">顺序</div>
                        <div class="grid-cell header-cell">排序</div>
                        <div class="grid-cell header-cell">宽度(px)</div>
                    </div>
                    <!-- 数据行 -->
                    <div v-for="(column, index) in processedColumns" :key="column.prop || index" class="grid-row">
                        <div class="grid-cell column-name-cell" :title="column.label">
                            <span>
                                {{ column.label }}
                            </span>
                        </div>
                        <div class="grid-cell">
                            <el-checkbox
                                v-model="column.columnManage.show"
                                class="compact-checkbox"
                                :disabled="column.columnManage.visible"
                            ></el-checkbox>
                        </div>
                        <div class="grid-cell">
                            <div class="arrow-buttons">
                                <button
                                    class="arrow-btn up-btn"
                                    :disabled="!canMoveUp(column, index)"
                                    @click="moveUp(index)"
                                    title="上移"
                                >
                                    ↑
                                </button>
                                <button
                                    class="arrow-btn down-btn"
                                    :disabled="!canMoveDown(column, index)"
                                    @click="moveDown(index)"
                                    title="下移"
                                >
                                    ↓
                                </button>
                            </div>
                        </div>
                        <div class="grid-cell">
                            <el-checkbox
                                @change="handleSortChange(column)"
                                v-model="column.sortable"
                                class="compact-checkbox"
                                true-label="custom"
                                :disabled="column.columnManage.sortableDisabled"
                            ></el-checkbox>
                        </div>
                        <div class="grid-cell">
                            <el-input
                                v-if="column.columnManage.widthType !== 'auto'"
                                v-model="column.width"
                                class="width-input"
                                maxlength="4"
                                size="mini"
                                placeholder="auto"
                                :disabled="column.columnManage.widthDisabled"
                                @blur="validateWidth(column)"
                                @input="handleWidthInput(column, $event)"
                            ></el-input>
                            <span v-else class="auto-width">auto</span>
                        </div>
                    </div>
                </div>
                <div class="save-button-container">
                    <el-button type="primary" size="small" @click="handleSave">保存</el-button>
                    <el-button size="small" @click="handleCancel">取消</el-button>
                </div>
            </div>
            <!-- 触发器 -->
            <template #reference>
                <el-button icon="el-icon-s-tools" size="small">列选择</el-button>
            </template>
        </el-popover>
    </div>
</template>

<script>
export default {
    name: 'SnbcTableColumnManage',
    components: {},
    props: {
        columns: {
            type: Array,
            required: true,
            default: () => []
        },
        configModule: {
            type: String,
            default: 'datatable'
        },
        configSection: {
            type: String,
            default: 'storyBrowse'
        },
        configKey: {
            type: String,
            default: 'cols'
        },
        // 是否将popover添加到body
        appendToBody: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            localColumns: this.initializeColumns(this.columns),
            originalColumns: this.initializeColumns(this.columns)
        };
    },
    computed: {
        // 处理后的列数据，将固定在第一行的列放在最前面
        processedColumns() {
            const columns = [...this.localColumns];
            const pinnedFirstColumns = [];
            const normalColumns = [];

            // 分离固定在第一行的列和普通列
            columns.forEach((column) => {
                if (column.columnManage && column.columnManage.pinnedFirst) {
                    pinnedFirstColumns.push(column);
                } else {
                    normalColumns.push(column);
                }
            });

            // 将固定列放在前面，普通列放在后面
            return [...pinnedFirstColumns, ...normalColumns];
        }
    },
    watch: {
        columns: {
            deep: true,
            handler(newVal) {
                this.localColumns = this.initializeColumns(newVal);
                this.originalColumns = this.initializeColumns(newVal);
            }
        }
    },
    async created() {
        // 组件创建时获取配置信息
        await this.getConfig();
    },
    methods: {
        /**
         * 初始化列数据，确保 columnManage 结构存在
         * @param {Array} columns - 原始列配置数组
         * @returns {Array} 初始化后的列配置数组
         */
        initializeColumns(columns) {
            return this.$tools.cloneDeep(columns).map((column) => {
                // 如果没有 columnManage 配置，则创建默认配置
                if (!column.columnManage) {
                    column.columnManage = {};
                }

                // 设置默认值
                const defaultConfig = {
                    show: column.show !== undefined ? column.show : true,
                    // 是否强制可见（不可取消选择）
                    visible: column.visible || false,
                    // 是否固定位置（不可移动）
                    fixed: column.fixed || false,
                    // 是否固定在第一行（永远保持在第一位）
                    pinnedFirst: column.pinnedFirst || false,
                    // 是否禁用排序选择
                    sortableDisabled: column.sortableDisabled || false,
                    // 是否禁用宽度编辑
                    widthDisabled: column.widthDisabled || false,
                    widthType: column.widthType || (column.width ? 'fixed' : 'auto')
                };

                // 合并默认配置和用户配置
                column.columnManage = { ...defaultConfig, ...column.columnManage };

                // 清理旧的直接属性（保持向后兼容）
                if (column.show !== undefined && column.columnManage.show === undefined) {
                    column.columnManage.show = column.show;
                }

                return column;
            });
        },
        /**
         * 保存列配置并触发变更事件
         * @emits columns-change 列配置变更事件
         */
        handleChange() {
            this.$emit('columns-change', this.localColumns);
        },
        /**
         * 交换两个元素
         * @param {Number} index1 第一个元素的索引
         * @param {Number} index2 第二个元素的索引
         */
        swapElements(index1, index2) {
            const newArray = [...this.localColumns];
            [newArray[index1], newArray[index2]] = [newArray[index2], newArray[index1]];
            this.localColumns = newArray;
        },
        /**
         * 检查列是否可以上移
         * @param {Object} column - 列配置对象
         * @param {Number} index - 列在显示数组中的索引
         * @returns {Boolean} 是否可以上移
         */
        canMoveUp(column, index) {
            if (column.columnManage && column.columnManage.pinnedFirst) return false;
            if (index === 0) return false;

            // 如果当前列是固定在第一行的，不能上移
            if (column.columnManage && column.columnManage.pinnedFirst) return false;

            // 如果上一行是固定在第一行的列，不能上移
            const prevColumn = this.processedColumns[index - 1];
            if (prevColumn && prevColumn.columnManage && prevColumn.columnManage.pinnedFirst) return false;

            return true;
        },

        /**
         * 检查列是否可以下移
         * @param {Object} column - 列配置对象
         * @param {Number} index - 列在显示数组中的索引
         * @returns {Boolean} 是否可以下移
         */
        canMoveDown(column, index) {
            if (column.columnManage && column.columnManage.pinnedFirst) return false;
            if (index === this.processedColumns.length - 1) return false;

            // 如果下一行是固定在第一行的列，不能下移
            const nextColumn = this.processedColumns[index + 1];
            if (nextColumn && nextColumn.columnManage && nextColumn.columnManage.pinnedFirst) return false;

            return true;
        },

        /**
         * 将指定列向上移动一位
         * @param {Number} index - 列在显示数组中的索引
         */
        moveUp(index) {
            const displayColumn = this.processedColumns[index];
            const realIndex = this.localColumns.findIndex((col) => col === displayColumn);
            const targetIndex = realIndex - 1;

            if (realIndex > 0 && this.canMoveUp(displayColumn, index)) {
                this.swapElements(realIndex, targetIndex);
            }
        },

        /**
         * 将指定列向下移动一位
         * @param {Number} index - 列在显示数组中的索引
         */
        moveDown(index) {
            const displayColumn = this.processedColumns[index];
            const realIndex = this.localColumns.findIndex((col) => col === displayColumn);
            const targetIndex = realIndex + 1;

            if (realIndex < this.localColumns.length - 1 && this.canMoveDown(displayColumn, index)) {
                this.swapElements(realIndex, targetIndex);
            }
        },
        /**
         * 保存列配置到服务器并关闭弹窗
         * @emits columns-change 列配置变更事件
         */
        handleSave() {
            // 确保固定在第一行的列始终在最前面
            const sortedColumns = this.sortColumnsByPinnedFirst(this.localColumns);
            this.localColumns = sortedColumns;
            this.originalColumns = this.$tools.cloneDeep(sortedColumns);
            this.saveConfig();
            this.$emit('columns-change', sortedColumns);
            this.$refs.columnPopover.doClose();
        },

        /**
         * 根据 pinnedFirst 配置对列进行排序，将固定列放在前面
         * @param {Array} columns - 列配置数组
         * @returns {Array} 排序后的列配置数组
         */
        sortColumnsByPinnedFirst(columns) {
            const pinnedFirstColumns = [];
            const normalColumns = [];

            columns.forEach((column) => {
                if (column.columnManage && column.columnManage.pinnedFirst) {
                    pinnedFirstColumns.push(column);
                } else {
                    normalColumns.push(column);
                }
            });

            return [...pinnedFirstColumns, ...normalColumns];
        },
        /**
         * 取消修改，恢复到原始状态并关闭弹窗
         */
        handleCancel() {
            this.localColumns = this.$tools.cloneDeep(this.originalColumns);
            this.$refs.columnPopover.doClose();
        },

        /**
         * 验证列宽度，确保不小于最小值120px
         * @param {Object} column - 列配置对象
         */
        validateWidth(column) {
            if (column.width && column.width !== '') {
                const width = parseInt(column.width);
                if (isNaN(width) || width < 120) {
                    column.width = '120';
                    this.$message.warning('列宽度不能小于120px，已自动调整为120px');
                }
            }
        },

        /**
         * 处理宽度输入，只允许输入数字
         * @param {Object} column - 列配置对象
         * @param {String} value - 输入的值
         */
        handleWidthInput(column, value) {
            // 只允许输入数字
            const numericValue = value.replace(/[^\d]/g, '');
            if (numericValue !== value) {
                column.width = numericValue;
            }
        },
        /**
         * 处理排序变化，因为checkbox的false选项不允许使用布尔值false，
         * 所以这里实际上是undifined，转为false供el-table使用
         * @param {Object} column - 列
         */
        handleSortChange(column) {
            if (!column.sortable) {
                column.sortable = false;
            }
        },
        /**
         * 保存列选择配置信息到服务器
         * @async
         */
        async saveConfig() {
            const api = this.$service.dms.common.saveColumnSelectInfo;
            // 过滤掉disabled相关配置，只保存用户可自定义的配置
            const columnsToSave = this.localColumns.map((column) => {
                const columnCopy = this.$tools.cloneDeep(column);
                if (columnCopy.columnManage) {
                    // 使用解构赋值移除disabled相关的配置，这些应该由外部Column配置控制
                    const {
                        visible,
                        fixed,
                        pinnedFirst,
                        sortableDisabled,
                        widthDisabled,
                        widthType,
                        ...allowedConfig
                    } = columnCopy.columnManage;
                    columnCopy.columnManage = allowedConfig;
                }
                // 移除render属性，不需要保存到后端
                const { render, ...restColumnCopy } = columnCopy;
                return restColumnCopy;
            });
            const params = {
                module: this.configModule,
                section: this.configSection,
                key: this.configKey,
                value: JSON.stringify(columnsToSave)
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('保存成功');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 从服务器获取列选择配置信息
         * @async
         * @emits columns-change 如果获取到配置则触发列配置变更事件
         */
        async getConfig() {
            const api = this.$service.dms.common.getColumnSelectInfo;
            const params = {
                module: this.configModule,
                section: this.configSection,
                key: this.configKey,
                owner: this.$store.state.user.userName
            };
            try {
                const res = await api(params);
                if (res.code === '0000' && res.data) {
                    // 获取到配置信息，解析并应用
                    const savedColumns = JSON.parse(res.data);
                    // 合并保存的配置和外部传入的Column配置，外部配置的disabled相关设置优先
                    const mergedColumns = this.mergeWithExternalConfig(savedColumns);
                    this.localColumns = this.initializeColumns(mergedColumns);
                    this.originalColumns = this.initializeColumns(mergedColumns);
                    this.$emit('columns-change', this.localColumns);
                }
            } catch (error) {
                console.error('获取配置信息失败，使用默认配置:', error);
            }
        },
        /**
         * 将保存的配置与外部传入的Column配置合并，外部配置的disabled相关设置优先
         * @param {Array} savedColumns - 从服务器获取的保存配置
         * @returns {Array} 合并后的列配置
         */
        mergeWithExternalConfig(savedColumns) {
            // 先处理外部传入的列配置
            const mergedColumns = this.columns.map((externalColumn) => {
                // 查找对应的保存配置
                const savedColumn = savedColumns.find(
                    (saved) =>
                        saved.prop === externalColumn.prop ||
                        (saved.label === externalColumn.label && !saved.prop && !externalColumn.prop)
                );

                if (savedColumn) {
                    // 合并配置，但保持外部配置的disabled相关设置
                    const merged = this.$tools.cloneDeep(savedColumn);
                    if (externalColumn.columnManage) {
                        if (!merged.columnManage) merged.columnManage = {};
                        // 用外部配置覆盖disabled相关设置
                        merged.columnManage.visible =
                            externalColumn.columnManage.visible || externalColumn.visible || false;
                        merged.columnManage.fixed = externalColumn.columnManage.fixed || externalColumn.fixed || false;
                        merged.columnManage.pinnedFirst =
                            externalColumn.columnManage.pinnedFirst || externalColumn.pinnedFirst || false;
                        merged.columnManage.sortableDisabled =
                            externalColumn.columnManage.sortableDisabled || externalColumn.sortableDisabled || false;
                        merged.columnManage.widthDisabled =
                            externalColumn.columnManage.widthDisabled || externalColumn.widthDisabled || false;
                        merged.columnManage.widthType =
                            externalColumn.columnManage.widthType ||
                            externalColumn.widthType ||
                            (externalColumn.width ? 'fixed' : 'auto');
                    }
                    return merged;
                }
                // 如果没有保存的配置，使用外部配置（新增的列，默认勾选显示）
                const newColumn = this.$tools.cloneDeep(externalColumn);
                if (!newColumn.columnManage) {
                    newColumn.columnManage = {};
                }
                // 新增列默认显示
                if (newColumn.columnManage.show === undefined) {
                    newColumn.columnManage.show = true;
                }
                return newColumn;
            });

            // 检查是否有保存配置中的列在外部配置中已经不存在了（被删除的列）
            // 这些列不应该出现在最终的配置中
            return mergedColumns;
        }
    }
};
</script>

<style scoped>
.column-manager-box {
    width: 450px;
    max-height: 550px;
    overflow-y: auto;
}

.grid-table {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    font-size: 12px;
    max-height: 450px;
    overflow-y: auto;
}

.grid-header,
.grid-row {
    display: grid;
    grid-template-columns: 1fr 45px 60px 45px 85px;
    border-bottom: 1px solid #ebeef5;
}

.grid-header {
    background-color: #f5f7fa;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
}

.grid-row:last-child {
    border-bottom: none;
}

.grid-row:hover {
    background-color: #f5f7fa;
}

.grid-cell {
    padding: 6px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #ebeef5;
    min-height: 32px;
    box-sizing: border-box;
}

.grid-cell:first-child {
    justify-content: flex-start;
    padding-left: 8px;
    font-size: 12px;
}

.grid-cell:last-child {
    border-right: none;
}

/* 列名称单元格样式 */
.column-name-cell {
    overflow: hidden;
}

.column-name-text {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    line-height: 1.2;
}

.column-name-text.pinned-first {
    color: #409eff;
    font-weight: 500;
}

.pinned-icon {
    margin-right: 4px;
    color: #409eff;
    font-size: 12px;
}

.header-cell {
    color: #909399;
    font-size: 12px;
    font-weight: 500;
}

.arrow-buttons {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.arrow-btn {
    width: 20px;
    height: 14px;
    border: 1px solid #dcdfe6;
    background: #fff;
    color: #606266;
    cursor: pointer;
    font-size: 10px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: all 0.1s;
}

.arrow-btn:hover:not(:disabled) {
    background: #ecf5ff;
    border-color: #409eff;
    color: #409eff;
}

.arrow-btn:disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background: #f5f7fa;
}

.width-input {
    width: 100%;
}

.auto-width {
    color: #909399;
    font-size: 11px;
}

::v-deep .compact-checkbox {
    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }
    .el-checkbox__inner::after {
        left: 4px;
        top: 1px;
        width: 4px;
        height: 8px;
        border-width: 1px;
    }
}

::v-deep .width-input.el-input--mini {
    .el-input__inner {
        height: 24px;
        line-height: 24px;
        padding: 0 6px;
        font-size: 12px;
        text-align: center;
    }
}

::v-deep .column-manager-popover {
    padding: 8px !important;
    min-width: auto !important;
}

.save-button-container {
    padding: 8px;
    text-align: center;
    border-top: 1px solid #ebeef5;
    margin-top: 0;
    background: #fafafa;
}

.save-button-container .el-button {
    margin: 0 4px;
    padding: 6px 12px;
}

/* 性能优化：减少重绘 */
.grid-row {
    will-change: background-color;
}

/* 滚动条优化 */
.column-manager-box::-webkit-scrollbar {
    width: 6px;
}

.column-manager-box::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.column-manager-box::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.column-manager-box::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

::v-deep .el-input__suffix {
    right: 0;
}
</style>
