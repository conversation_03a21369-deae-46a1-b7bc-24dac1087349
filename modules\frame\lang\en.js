/**
 * 英文国际化配置：项目模块国际化配置文件route和project必填、httpCode可选
 * @param {Object} route 项目模块路由国际化
 * @param {Object} httpCode 项目模块httpCode国际化
 * @param {Object} project 项目模块除路由、httpCode外，其他信息国际化
 */
export default {
    route: {
        userManagement: 'userManagement',
        personManagement: 'personManagement',
        changePwd: 'changePwd'
    },
    project: {
        systmeTitle: 'Everyone to take',
        changePwd: 'changePwd',
        msg: {
            handling: 'Processing, please wait...'
        }
    },
    httpCode: {
        http401: 'No permission or permission has expired. Please log in again.',
        http404: 'Can not find the interface',
        http500: 'Unable to access the server'
    }
};
