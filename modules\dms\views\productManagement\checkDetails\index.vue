<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="cancel">返回</el-button>
        </div>
        <div>
            <!-- 产品结构 -->
            <formula-title :title="basicTitle"></formula-title>
            <!-- 操作记录 -->
            <formula-title :title="operationTitle"></formula-title>
            <operation-record :collapseItems="collapseItems"></operation-record>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import operationRecord from '../components/operationRecord.vue';

export default {
    components: { formulaTitle, operationRecord },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '产品结构',
            operationTitle: '操作记录',
            objectId: '',
            collapseItems: [
                // {
                //     name: '1',
                //     date: '2025-06-17 17:27:16',
                //     operator: '钟振国',
                //     status: '创建',
                //     opinion: '创建任务初始记录'
                // },
                // {
                //     name: '2',
                //     date: '2025-06-17 17:35:00',
                //     operator: '钟振国',
                //     status: '拒绝',
                //     opinion: '创建任务初始记录'
                // },
                // {
                //     name: '3',
                //     date: '2025-06-18 16:41:34',
                //     operator: '于淼',
                //     status: '通过',
                //     opinion: '创建任务初始记录'
                // },
                // {
                //     name: '4',
                //     date: '2025-06-19 19:06:11',
                //     operator: '钟振国',
                //     status: '编辑',
                //     opinion: '调整任务截止日期和版本'
                // }
            ]
        };
    },
    mounted() {
        this.objectId = this.$route.query.id;
        this.getActions(this.objectId);
    },
    methods: {
        async getActions(id) {
            try {
                const params = {
                    objectId: id
                };
                const res = await this.$service.dms.product.getActions(params);
                if (res.code === '0000') {
                    this.collapseItems = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        cancel() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped></style>
