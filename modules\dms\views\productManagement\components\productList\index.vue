<template>
    <common-list
        v-model="activeNavTab"
        :nav-items="navItems"
        :query-params="queryParams"
        :query-config="queryConfig"
        :columns="columns"
        :data="data"
        :load-data="loadData"
        :actions-width="actionsWidth"
        :actions-fixed="actionsFixed"
        :default-search-open="defaultSearchOpen"
        :table-attrs="tableAttrs"
        :total="total"
        :page="page"
        :limit="limit"
        :row-key="rowKey"
        :selectable="selectable"
        :config-module="configModule"
        :config-section="configSection"
        :config-key="configKey"
        v-on="$listeners"
    >
        <!-- 透传所有插槽 -->
        <template v-for="slot in Object.keys($scopedSlots)" #[slot]="scope">
            <slot :name="slot" v-bind="scope"></slot>
        </template>
    </common-list>
</template>

<script>
import CommonList from 'dms/components/CommonList/sort.vue';

export default {
    name: 'ProductList',
    components: {
        CommonList
    },
    props: {
        // v-model 绑定的值（当前激活的导航标签）
        value: {
            type: String,
            default: ''
        },
        // 导航栏配置
        navItems: {
            type: Array,
            default: () => []
        },
        // 查询参数
        queryParams: {
            type: Object,
            default: () => ({})
        },
        // 查询表单配置
        queryConfig: {
            type: Object,
            default: () => ({
                elFormAttrs: {},
                items: []
            })
        },
        // 表格列配置（包含所有可能的列）
        columns: {
            type: Array,
            default: () => []
        },
        // 表格数据
        data: {
            type: Array,
            default: () => []
        },
        // 数据加载函数
        loadData: {
            type: Function,
            required: false
        },
        // 操作列宽度
        actionsWidth: {
            type: [String, Number],
            default: 150
        },
        // 操作列是否固定
        actionsFixed: {
            type: [String, Boolean],
            default: 'right'
        },
        // 默认搜索面板是否打开
        defaultSearchOpen: {
            type: Boolean,
            default: false
        },
        // 表格其他属性
        tableAttrs: {
            type: Object,
            default: () => ({})
        },
        // 分页配置
        total: {
            type: Number,
            default: 0
        },
        page: {
            type: Number,
            default: 1
        },
        limit: {
            type: Number,
            default: 10
        },
        // 行数据的key
        rowKey: {
            type: [String, Function],
            default: 'teamId'
        },
        // 自定义行是否可选择的函数
        selectable: {
            type: Function,
            default: null
        },
        // 列配置存储参数
        configModule: {
            type: String,
            default: 'datatable'
        },
        configSection: {
            type: String,
            default: 'groupBrowse'
        },
        configKey: {
            type: String,
            default: 'cols'
        }
    },
    computed: {
        // 当前激活的导航标签（支持 v-model）
        activeNavTab: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        }
    }
};
</script>
