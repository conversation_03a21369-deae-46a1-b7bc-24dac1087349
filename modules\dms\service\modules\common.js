/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 公共接口
        common: {
            // 获取全软件平台人员
            getDepartmentEmployeeList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/common/getProUser',
                    method: 'get',
                    params: data
                });
            },
            // 获取部门菜单列表
            getOrgList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/org/getOrgTree',
                    method: 'post',
                    data
                });
            },
            // 获取顶部级联选择框（项目/团队），入参projectType： P-项目 T-团队 空-所有
            getProjectOrGroupList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/project/getProjectsPermission',
                    method: 'post',
                    data
                });
            },
            // 根据项目ID获取团队ID获取对应的小组ID
            getTeamIdByProjectIdOrGroupId(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/team/getTeamIdByObjectId',
                    method: 'get',
                    params: data
                });
            },
            // 获取员工信息（0-在职 1-全部）
            getEmployeeList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/employee/getEmployees',
                    method: 'get',
                    params: data
                });
            },
            // 保存列选择信息
            saveColumnSelectInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/config/addConfigInfo',
                    method: 'post',
                    data
                });
            },
            // 获取列选择信息
            getColumnSelectInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/config/getConfigInfo',
                    method: 'post',
                    data
                });
            },
            // 项目/团队下拉框对应查询接口 P-项目 T-团队 空-所有
            getProjectOrGroupListWithOutPermission(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/project/selectProjects',
                    method: 'post',
                    data
                });
            },
            // 查询产品列表
            getProductList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'product/getProduct',
                    method: 'post',
                    data
                });
            },
            // 查询产品线列表
            getProductLineList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'productLine/selectProductLines',
                    method: 'get',
                    params: data
                });
            },
            // 批量查询配置信息
            getAllConfigInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/config/getConfigList',
                    method: 'post',
                    data
                });
            },
            // 批量更新配置信息
            batchUpdateConfigInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/config/updateConfigList',
                    method: 'post',
                    data
                });
            },
            // 获取一级部门及二级部门
            getFirstAndSecondLevelDepartment(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/org/getOrgChildren',
                    method: 'get',
                    params: data
                });
            }
        }
    };

    return service;
};
