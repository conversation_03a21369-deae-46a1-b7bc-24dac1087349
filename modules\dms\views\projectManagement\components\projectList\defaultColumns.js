export const defaultColumns = [
    {
        type: 'selection',
        width: 55,
        label: '选择框',
        columnManage: {
            sortableDisabled: true,
            // 固定在第一行
            pinnedFirst: true,
            widthDisabled: true
        }
    },
    {
        prop: 'projectId',
        label: '项目ID',
        width: 140,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '项目名称',
        width: 300,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectLeader',
        label: '项目负责人',
        width: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectType',
        label: '项目类型',
        width: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'departmentName',
        label: '所属部门',
        width: 200,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectStatus',
        label: '项目状态',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'priority',
        label: '优先级',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'startDate',
        label: '开始日期',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'endDate',
        label: '结束日期',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'progress',
        label: '进度',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'budget',
        label: '预算',
        width: 120,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'actualCost',
        label: '实际成本',
        width: 120,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'memberCount',
        label: '成员数量',
        width: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'createTime',
        label: '创建时间',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'updateTime',
        label: '更新时间',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    }
];
