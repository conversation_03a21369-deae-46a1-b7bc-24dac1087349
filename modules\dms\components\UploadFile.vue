<template>
    <div>
        <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="headers"
            :file-list="fileList"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :limit="maxCount"
            :data="{ uploadPath: 'story' }"
            :show-file-list="false"
            @update:fileList="handleFileListUpdate"
            multiple
        >
            <el-button slot="trigger" size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
                只能上传{{ accept.join(', ') }}文件，且不超过{{ maxSize }}MB，最多上传{{ maxCount }}个
            </div>
        </el-upload>
        <div v-if="fileList.length > 0" class="file-list">
            <div v-for="(file, index) in fileList" :key="index" class="file-item">
                <div class="file-info">
                    <i class="el-icon-document"></i>
                    <span class="file-name">{{ file.name }}</span>
                </div>
                <el-button type="text" @click="downloadFile(file, fileList)" class="download-btn">
                    <i class="el-icon-download"></i>
                    下载
                </el-button>
                <el-button type="text" @click="handleRemove(file, fileList)" class="delete-btn">
                    <i class="el-icon-delete"></i>
                    删除
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'UploadComponent',
    props: {
        uploadUrl: {
            type: String,
            required: true
        },
        headers: {
            type: Object,
            default: () => ({})
        },
        maxSize: {
            type: Number,
            default: 20
        },
        accept: {
            type: Array,
            default: () => [
                'png',
                'jpg',
                'jpeg',
                'gif',
                'bmp',
                'pdf',
                'pptx',
                'doc',
                'docx',
                'xlsx',
                'zip',
                '7z',
                'rar'
            ]
        },
        maxCount: {
            type: Number,
            default: 5
        },
        initialFileList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            fileList: [],
            pathName: ''
        };
    },
    watch: {
        initialFileList: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    this.fileList = newVal.map((file) => ({
                        name: file.title,
                        url: file.pathName,
                        uid: file.id,
                        status: 'success',
                        response: {
                            data: file.id
                        }
                    }));
                    // 更新父组件的fileId
                    const fileIds = newVal.map((file) => file.id);
                    this.$emit('init-file-ids', fileIds);
                }
            },
            immediate: true
        }
    },
    methods: {
        beforeUpload(file) {
            const fileType = file.name.split('.').pop().toLowerCase();
            const fileSize = file.size / 1024 / 1024;
            if (!this.accept.includes(fileType)) {
                this.$message.error(`只能上传${this.accept.join(', ')}类型的文件`);
                return Promise.reject();
            }
            if (fileSize > this.maxSize) {
                this.$message.error(`文件大小不能超过${this.maxSize}MB`);
                return Promise.reject();
            }
        },
        handleSuccess(response, file, fileList) {
            this.pathName = response.data.pathName;
            this.fileList = fileList;
            this.$emit('upload-success', response, file, fileList);
        },
        handleRemove(file, fileList) {
            const params = {
                fileId: file.response ? file.response.data : file.uid
            };
            this.$service.dms.original
                .deleteFile(params)
                .then((res) => {
                    if (res.code === '0000') {
                        this.$message.success(res.message);
                        const { uid } = file;
                        const updatedFileList = fileList.filter((item) => item.uid !== uid);
                        this.fileList = updatedFileList;
                        this.$emit('remove-file', file);
                    } else {
                        this.$message.error(res.message);
                    }
                })
                .catch((error) => {
                    console.error('删除文件失败:', error);
                    this.$message.error('删除失败');
                });
        },
        async downloadFile(file, fileList) {
            try {
                const params = {
                    fileId: file.response ? file.response.data : file.uid
                };
                const fileName = file.name.split('.')[0];
                const fileExtension = file.name.split('.').pop();
                const stream = await this.$service.dms.original.xzFile({ ...params });
                this.$tools.downloadExprotFile(stream, fileName, fileExtension).catch((e) => {
                    this.$tools.message.err(e || '下载失败');
                });
            } catch (error) {
                console.error('下载文件失败:', error);
                this.$message.error('下载失败');
            }
        },
        handleFileListUpdate(updatedFileList) {
            this.fileList = updatedFileList;
        }
    }
};
</script>

<style lang="scss" scoped>
.upload-demo {
    margin-bottom: 20px;
}

.file-list {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 8px;
    background-color: #fafbfc;

    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
            border-bottom: none;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;

            .el-icon-document {
                color: #409eff;
                margin-right: 8px;
                font-size: 16px;
            }

            .file-name {
                color: #303133;
                font-size: 14px;
                margin-right: 8px;
                word-break: break-all;
            }

            .file-size {
                color: #909399;
                font-size: 12px;
            }
        }

        .download-btn {
            color: #409eff;
            padding: 4px 8px;

            &:hover {
                background-color: #ecf5ff;
                border-radius: 4px;
            }

            .el-icon-download {
                margin-right: 4px;
            }
        }
        .delete-btn {
            color: #f56c6c;
            padding: 4px 8px;

            &:hover {
                background-color: #ecf5ff;
                border-radius: 4px;
            }

            .el-icon-download {
                margin-right: 4px;
            }
        }
    }
}
</style>
