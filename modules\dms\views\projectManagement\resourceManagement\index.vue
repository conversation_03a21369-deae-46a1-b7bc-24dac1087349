<template>
    <div>
        <ProjectSelector :key="projectSelectorKey"></ProjectSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="工时负载" name="resourceLoad" :lazy="true">
                    <ResourceLoad class="mt-15" :activeName="activeName"></ResourceLoad>
                </el-tab-pane>
                <el-tab-pane label="项目成员" name="projectManagementOrgChart">
                    <ProjectOrgChart
                        @updateTopList="updateTopList"
                        :activeName="activeName"
                        :isProject="true"
                        :showOrgChart="!!projectId"
                        @relate-team-success="updateTopList"
                    ></ProjectOrgChart>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import ProjectOrgChart from 'dms/components/projectOrgChart/index.vue';
import ResourceLoad from './components/resourceLoad.vue';
import ProjectSelector from 'dms/components/TopSelectorGroup/ProjectSelector.vue';

export default {
    name: 'Resources',
    components: {
        ProjectOrgChart,
        ProjectSelector,
        ResourceLoad
    },
    data() {
        return {
            activeName: 'resourceLoad',
            orgChartData: {},
            // 顶部级联组件key
            projectSelectorKey: 0,
            department: ['软件开发部']
        };
    },
    computed: {
        projectId() {
            return this.$store.state.dms.project;
        }
    },
    watch: {
        projectId(newVal) {
            if (newVal) {
                this.handleChange(newVal);
            }
        }
    },
    beforeDestroy() {
        this.$store.dispatch('dms/setDepartment', []);
        this.$store.dispatch('dms/setDepartmentOption', []);
    },
    activated() {
        // 因为做了缓存，其他页面改变项目之后本页面的级联选择器不会同步，改变key令其同步
        this.projectSelectorKey += 1;
    },
    methods: {
        async handleChange(value) {
            if (!value) return;
            const api = this.$service.dms.common.getTeamIdByProjectIdOrGroupId;
            const params = {
                objectId: value
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$store.dispatch('dms/setDepartment', ['', '', res.data]);
                this.$store.dispatch('dms/setDepartmentOption', []);
            } catch (error) {
                console.error('Error:', error);
            }
        },
        updateTopList() {
            this.projectSelectorKey += 1;
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 10px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.mt-15 {
    margin-top: 15px;
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
.resourceLoad {
    margin-top: 20px;
}
</style>
