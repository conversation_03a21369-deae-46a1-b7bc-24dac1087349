<template>
    <el-select
        v-model="members"
        :placeholder="placeholder"
        filterable
        :multiple="isMultipled"
        ref="refElSelect"
        class="custom-select"
        @change="handleMemberChange"
        @blur="handleBlur"
        :size="size"
        :remote="isRemote"
        :remote-method="remoteMethod"
        :disabled="disabled"
        :clearable="clearable"
        :collapse-tags="isCollapseTags"
        :default-first-option="'default-first-option'"
    >
        <el-option
            v-for="item in innerOptions"
            :key="item.account"
            :label="item.realname"
            :value="item.account"
            class="custom-option"
        >
        </el-option>
    </el-select>
</template>

<script>
/**
 * 封装了软件所有人员的人员选择
 */
import emitter from 'element-ui/src/mixins/emitter';

const {
    methods: { dispatch }
} = emitter;

export default {
    name: 'DepartmentPeopleSelector',
    props: {
        // 二级部门code，不传则默认查询技术中心所有人员
        'orgCode': {
            type: String,
            default: ''
        },
        'placeholder': {
            type: String,
            default: '请输入'
        },
        // 是否多选
        'isMultipled': {
            type: Boolean,
            default: true
        },
        'size': {
            type: String,
            default: 'mini'
        },
        'value': {
            type: [String, Array],
            default: () => []
        },
        'disabled': {
            type: Boolean,
            default: false
        },
        'clearable': {
            type: Boolean,
            default: true
        },
        'options': {
            type: Array,
            default: () => []
        },
        'isCollapseTags': {
            type: Boolean,
            default: false
        },
        'isRemote': {
            type: Boolean,
            default: true
        },
        'default-first-option': {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            members: this.value,
            innerOptions: []
        };
    },
    computed: {
        // 全部人员数据
        allPersonData() {
            return this.$store.state.dms.departmentEmployeeList;
        }
    },
    watch: {
        value(newValue) {
            this.members = newValue;
            this.getOptions();
        },
        // 如果一个页面存在多个人员选择组件，只会有一个进行全部人员的查询，
        // 查询结束之后更新其他组件的option
        allPersonData(newValue) {
            if (newValue.length !== 0) {
                this.getOptions();
            }
        },
        options(newVal) {
            newVal && this.getOptions();
        }
    },
    created() {
        this.getEmployeeList();
    },
    mounted() {
        // 进入首页时会查出全部人员名单，可以直接构建options
        if (this.allPersonData.length !== 0 || this.options.length !== 0) {
            this.getOptions();
        }
    },
    activated() {
        if (this.allPersonData.length !== 0 || this.options.length !== 0) {
            this.getOptions();
        }
    },
    beforeDestroy() {
        // 跨页面使用该组件，保证刷新之后组件能够及时查询
        this.$store.dispatch('dms/changeEmployeeQueried', false);
    },
    methods: {
        /**
         * 获取所有人员的数据
         */
        async getEmployeeList() {
            const queried = this?.$store?.state?.dms?.employeeQueried;
            // 一个页面多个组件的情况，查询过之后不再重复查询
            if (queried) {
                this.getOptions();
                return;
            }
            await this.$store.dispatch('dms/changeEmployeeQueried', true);
            const hasData = this?.$store?.state?.dms?.departmentEmployeeList?.length !== 0;
            // 第一次查过之后从store里面拿值
            if (hasData) {
                return;
            }
            try {
                const params = {
                    departId: ''
                };
                const res = await this.$service.dms.common.getDepartmentEmployeeList(params);
                if (res.code === '0000') {
                    this.$store.dispatch('dms/changeDepartmentEmployeeList', res.data || []);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 人员变更
         */
        handleMemberChange() {
            // 触发el-form的表单校验
            dispatch.call(this, 'ElFormItem', 'el.form.change', this.value);
            this.getOptions();
            if (Array.isArray(this.members) && !this.isMultipled) {
                this.$emit('input', this.members[0]);
            } else {
                this.$emit('input', this.members);
            }
        },
        /**
         * 失焦时的处理
         */
        handleBlur() {
            // 触发el-form的表单校验
            dispatch.call(this, 'ElFormItem', 'el.form.blur', this.value);
        },
        /**
         * 远程搜索，解决过量数据卡顿问题
         * @param {Object} query 参数
         */
        remoteMethod(query) {
            if (query !== '') {
                // 外部传入数据，用外部的option搜索
                const sourceData = this.options.length > 0 ? this.options : this.allPersonData;
                this.innerOptions = sourceData.filter((item) => {
                    return item.realname.indexOf(query) > -1;
                });
            } else {
                this.innerOptions = [];
            }
        },
        /**
         * 处理多选/单选对应的option,
         * 主要用于反显的情况
         */
        getOptions() {
            let fitlerOptions = this.allPersonData;

            // 如果外部传入option，使用外部的option
            if (this.options.length > 0) {
                fitlerOptions = this.options;
            }
            // 如果不进行远程搜索，直接返回所有options
            if (!this.isRemote) {
                this.innerOptions = fitlerOptions;
                return;
            }
            if (Array.isArray(this.value)) {
                if (this.value.length === 0) return;
                this.innerOptions = fitlerOptions.filter((item) => {
                    return this.value.includes(item.account);
                });
            } else if (this.value !== '') {
                this.innerOptions = fitlerOptions.filter((item) => item.account === this.value);
            } else {
                this.innerOptions = [];
            }
        }
    }
};
</script>
