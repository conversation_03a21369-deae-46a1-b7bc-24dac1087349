<template>
    <div class="resource-load">
        <div class="tabGroup">
            <el-radio-group v-model="view" style="align-self: flex-start">
                <el-radio-button label="day">日视图</el-radio-button>
                <el-radio-button label="month">月视图</el-radio-button>
            </el-radio-group>
            <div class="status-info" v-show="showType === 'table'">
                <span class="status-info--low">工时<={{ view === 'day' ? 8 : 168 }}小时</span>
                <span class="status-info--mid">工时{{ view === 'day' ? '8-12' : '168-252' }}小时</span>
                <span class="status-info--high">工时>{{ view === 'day' ? 12 : 252 }}小时</span>
            </div>
            <div class="filter" v-show="view === 'day'">
                <el-button icon="el-icon-arrow-left" @click="prevMonth">上月</el-button>
                <el-date-picker
                    v-model="selectedDate"
                    type="month"
                    placeholder="选择月"
                    @change="handleChange()"
                    :clearable="false"
                >
                </el-date-picker>
                <el-button @click="nextMonth">下月<i class="el-icon-arrow-right"></i></el-button>
            </div>
            <div class="filter" v-show="view === 'month'">
                <el-button icon="el-icon-arrow-left" @click="prevYear">上一年</el-button>
                <el-date-picker
                    v-show="view === 'month'"
                    v-model="selectedDate"
                    type="year"
                    placeholder="选择年"
                    @change="handleChange()"
                    :clearable="false"
                >
                </el-date-picker>
                <el-button @click="nextYear">下一年<i class="el-icon-arrow-right"></i></el-button>
            </div>
            <!-- 保证月份选择居中 -->
            <el-radio-group style="align-self: flex-end" v-model="showType">
                <el-radio-button label="table">
                    <!-- 这个table图标在wtf-core-vue-ng中 -->
                    <svg-icon icon-class="table" class="select-icon" />
                </el-radio-button>
                <el-radio-button label="Chart">
                    <svg-icon icon-class="dms-lineChart" class="select-icon" v-show="this.view === 'day'" />
                    <svg-icon icon-class="dms-barChart" class="select-icon" v-show="this.view === 'month'" />
                </el-radio-button>
            </el-radio-group>
        </div>
        <div id="resourceChart" class="resourceChart" v-show="showType !== 'table'"></div>
        <ElTableVirtualScroll
            v-show="showType === 'table'"
            :data="allData"
            :height="30"
            @change="(renderData) => (staffList = renderData)"
            keyProp="id"
            :throttleTime="5"
            :buffer="400"
        >
            <el-table
                ref="resourceLoadRef"
                :data="staffList"
                :header-cell-style="{
                    'text-align': 'center'
                }"
                border
                :height="'calc(100vh - 180px)'"
                row-key="id"
                class="resourceTable"
                lazy
                :load="load"
                :tree-props="{
                    hasChildren: 'hasChildren'
                }"
            >
                <el-table-column
                    :label="userName"
                    min-width="200px"
                    :resizable="false"
                    :show-overflow-tooltip="true"
                    class-name="task-type"
                >
                    <template slot-scope="scope">
                        {{ scope.row.proProjectId ? scope.row.proProjectName : scope.row.taskType }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-for="(item, index) in this.view === 'day' ? getDaysInMonth(year, month) : getMonthsInYear()"
                    :key="index"
                    :resizable="false"
                    :min-width="columnWidth"
                    :class-name="getHeaderClassName(index)"
                >
                    <template slot="header">
                        <div
                            :class="[abnormalWorkHoursClass(index), hanlderTableHeader(index)]"
                            :title="computedTooltip(index)"
                        >
                            <span class="fz-12"> {{ item }}</span>
                        </div>
                    </template>
                    <template slot-scope="scope">
                        <WorkTimeBar
                            :taskList="scope.row.hoursViewShowVoList[item - 1] || {}"
                            :view="view"
                            @click="
                                workTimeBarClick([
                                    scope.row,
                                    scope.row.hoursViewShowVoList[item - 1],
                                    index >= dividedNumber - 1
                                ])
                            "
                            :isLast="scope.row.person === '汇总'"
                            :isPlanHour="index >= dividedNumber - 1"
                            :isNormal="abnormalWorkHoursClass(index) === ''"
                        ></WorkTimeBar>
                    </template>
                </el-table-column>
            </el-table>
        </ElTableVirtualScroll>
    </div>
</template>

<script>
import WorkTimeBar from './workTimeBar.vue';
import ElTableVirtualScroll from 'dms/components/ElTableVirtualScroll.vue';
import { Loading } from 'element-ui';
import i18n from 'wtf-core-vue-ng/src/lang';
import * as echarts from 'echarts';
import { getResoureLoadChartByDay, getResoureLoadChartByMonth } from './resourceLoadChart';

export default {
    name: 'PersonResourceLoad',
    components: { WorkTimeBar, ElTableVirtualScroll },
    props: {
        // 域账号
        loginName: { type: String, default: '' },
        // 姓名
        userName: { type: String, default: '' },
        // 视图
        inputView: { type: String, default: '' },
        // 日期
        date: { type: String, default: '' }
    },
    data() {
        return {
            // 表格可视区域渲染的数据
            staffList: [],
            // 存储所有表格数据
            allData: [],
            // 日期
            selectedDate: new Date(),
            // 日/月视图
            view: 'day',
            // 可视列表的开始索引
            firstItemIndex: 0,
            // 可视列表的结束索引
            lastItemIndex: 20,
            // loading状态
            workTimeLoading: true,
            // 表格还是图表
            showType: 'table',
            // 从这一天开始是预计负载
            dividedNumber: 32,
            // 个人资源负载-日视图查询-图
            getChartDataByDay: this.$service.dms.presonalResourceLoad.getChartDataByDay,
            // 个人资源负载-月视图查询-图
            getChartDataByMonth: this.$service.dms.presonalResourceLoad.getChartDataByMonth,
            // 个人资源负载-日视图查询-任务详情
            getTaskDetailByDay: this.$service.dms.presonalResourceLoad.getTaskDetailByDay,
            // 个人资源负载-月视图查询-任务详情
            getTaskDetailByMonth: this.$service.dms.presonalResourceLoad.getTaskDetailByMonth,
            // 个人资源负载-日视图-任务详情-图
            getTaskDetailChartDataByDay: this.$service.dms.presonalResourceLoad.getTaskDetailChartDataByDay,
            // 个人资源负载-月视图-任务详情-图
            getTaskDetailChartDataByMonth: this.$service.dms.presonalResourceLoad.getTaskDetailChartDataByMonth,
            // 个人资源负载-日视图查询-表格-任务类型列表
            getTaskListTableByDay: this.$service.dms.presonalResourceLoad.getTaskListTableByDay,
            // 个人资源负载-月视图查询-表格-任务类型列表
            getTaskListTableByMonth: this.$service.dms.presonalResourceLoad.getTaskListTableByMonth,
            // 个人资源负载-日视图查询-表格-任务类型下的项目列表
            getProjectListByDay: this.$service.dms.presonalResourceLoad.getProjectListByDay,
            // 个人资源负载-月视图查询-表格-任务类型下的项目列表
            getProjectListByMonth: this.$service.dms.presonalResourceLoad.getProjectListByMonth
        };
    },
    computed: {
        // 表格列宽
        columnWidth() {
            if (this.view === 'month') {
                return 70;
            }
            return 20;
        },
        daysNumInMonth() {
            return this.getDaysInMonth(this.year, this.month).length;
        },
        year() {
            return this.selectedDate.getFullYear();
        },
        month() {
            return this.selectedDate.getMonth() + 1;
        },
        curDayOrMonth() {
            if (this.view === 'day') {
                return new Date().getDate();
            }
            return new Date().getMonth() + 1;
        }
    },
    watch: {
        selectedDate() {
            this.handleChange();
        },
        view() {
            this.handleChange();
        },
        showType() {
            this.handleChange();
        }
    },
    mounted() {
        if (this.$route?.query?.name) {
            this.selectedDate = new Date(this.date);
            this.view = this.inputView;
        }
        this.handleChange(this.view);
    },
    activated() {
        if (this.$route?.query?.name) {
            this.selectedDate = new Date(this.date);
            this.view = this.inputView;
        }
        this.handleChange(this.view);
    },
    methods: {
        /**
         * 获取选择的日期
         * @param {String} type 日视图或月视图
         * @return {String} 选择的日期
         */
        getSelectedDate(type) {
            let selectDate;
            if (type === 'day' && this.month <= 9) {
                selectDate = `${this.year}-0${this.month}`;
            } else if (type === 'day' && this.month > 9) {
                selectDate = `${this.year}-${this.month}`;
            } else if (type === 'month') {
                selectDate = `${this.year}`;
            }
            return selectDate;
        },
        /**
         * 获取当月天数
         * @param {*} year 当年
         * @param {*} month 当月
         * @returns {Array} 当月天数的数组
         */
        getDaysInMonth(year, month) {
            const days = new Date(year, month, 0).getDate();
            return Array.from({ length: days }, (v, k) => k + 1);
        },
        /**
         * 获取一年中的月份
         * @returns {Array} 一年中月份的数组
         */
        getMonthsInYear() {
            return Array.from({ length: 12 }, (v, k) => k + 1);
        },
        /**
         * 处理参数变更
         */
        handleChange() {
            if (!this.loginName) return;
            this.dividedNumber = 32;
            const loadingInstance = Loading.service({
                text: i18n.t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            this.allData = [];
            const selectDate = this.getSelectedDate(this.view);
            const requestData = {
                loginNames: [this.loginName],
                selectDate
            };
            let api;
            if (this.view === 'day') {
                if (this.showType === 'table') {
                    api = this.getTaskListTableByDay;
                } else {
                    api = this.getChartDataByDay;
                }
            } else if (this.showType === 'table') {
                api = this.getTaskListTableByMonth;
            } else {
                api = this.getChartDataByMonth;
            }
            api(requestData)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.dividedNumber = res.body[0].dividedNumber;
                        if (this.showType === 'table') {
                            this.allData = res.body.map((i) => {
                                i.id = `${i.taskType}${i.proProjectId}`;
                                i.hoursViewShowVoList.map((j) => {
                                    j.taskShowVoList = [];
                                    return j;
                                });
                                return i;
                            });
                        } else {
                            this.setResourceChart(res);
                        }
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                })
                .finally(() => {
                    loadingInstance && loadingInstance.close();
                });
        },
        /**
         * 上一月
         */
        prevMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 0) {
                this.selectedDate = new Date(currentYear - 1, 11, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth - 1, 1);
            }
        },
        /**
         * 下一月
         */
        nextMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 11) {
                this.selectedDate = new Date(currentYear + 1, 0, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth + 1, 1);
            }
        },
        /**
         * 上一年
         */
        prevYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear - 1, 0, 1);
        },
        /**
         * 下一年
         */
        nextYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear + 1, 0, 1);
        },
        /**
         * 点击工时获取任务详情
         * @param {Array} value 工时信息
         */
        workTimeBarClick(value) {
            const { taskType, proProjectId } = value[0];
            const { time } = value[1];
            const isQueryPlanHour = value[2];
            const api = this.view === 'day' ? this.getTaskDetailByDay : this.getTaskDetailByMonth;
            let params;
            if (!proProjectId) {
                params = {
                    loginNames: [this.loginName],
                    selectDate: time,
                    taskType,
                    isQueryPlanHour
                };
            } else {
                params = {
                    loginNames: [this.loginName],
                    selectDate: time,
                    taskType,
                    projectId: proProjectId,
                    isQueryPlanHour
                };
            }
            api(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        value[1].taskShowVoList = res.body;
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        /**
         * 懒加载树型节点数据
         * @param {*} tree 当前节点
         * @param {*} treeNode  树型节点
         * @param {*} resolve  回调
         */
        load(tree, treeNode, resolve) {
            const { taskType } = tree;
            let api;
            if (this.view === 'day') {
                api = this.getProjectListByDay;
            } else {
                api = this.getProjectListByMonth;
            }
            const selectDate = this.getSelectedDate(this.view);
            const params = {
                loginNames: [this.loginName],
                taskType,
                selectDate
            };
            api(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        const data = res.body.map((i) => {
                            i.id = `${i.taskType}${i.proProjectId}`;
                            i.hoursViewShowVoList.map((j) => {
                                j.taskShowVoList = [];
                                return j;
                            });
                            return i;
                        });
                        resolve(data);
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    this.$message.error('系统异常');
                    console.error('ERROR:', err);
                });
        },
        /**
         * 当前日期高亮
         * @param {Number} columnIndex 索引
         * @returns {String} 样式
         */
        hanlderTableHeader(columnIndex) {
            let curDayOrMonth;
            if (this.selectedDate.getFullYear() !== new Date().getFullYear()) {
                return '';
            }
            if (this.view === 'day') {
                if (this.selectedDate.getMonth() !== new Date().getMonth()) {
                    return '';
                }
                curDayOrMonth = new Date().getDate();
            } else {
                curDayOrMonth = new Date().getMonth() + 1;
            }
            if (columnIndex + 1 === curDayOrMonth) {
                return 'curDate';
            }
        },
        /**
         * 绘制图表
         * @param {Object} res  图表数据
         */
        setResourceChart(res) {
            this.dividedNumber = res.body[0].dividedNumber;
            const myChart = echarts.init(document.getElementById('resourceChart'));
            // 清除上次的option，避免option合并
            myChart.clear();
            // 每天的任务数据，用于获取时间
            const wholeList = res.body[0].hoursViewShowVoList;
            let option;
            if (this.view === 'day') {
                const getDetailApi = this.getTaskDetailChartDataByDay;
                const hourDataList = res.body[0].hoursViewShowVoList.map((item, index) => {
                    if (index + 1 < this.dividedNumber) {
                        // 空字符串会被parseFloat转换为NaN
                        return parseFloat(item.hours);
                    }
                    // 空字符串会被parseFloat转换为NaN
                    return parseFloat(item.planHours);
                });
                option = getResoureLoadChartByDay(
                    this,
                    hourDataList,
                    this.dividedNumber,
                    getDetailApi,
                    wholeList,
                    this.daysNumInMonth
                );
            } else {
                const getDetailApi = this.getTaskDetailChartDataByMonth;
                const actualHourList = res.body[0].hoursViewShowVoList.map((item, index) => {
                    return index <= this.dividedNumber - 1 ? parseFloat(item.hours) : '';
                });
                const planHourList = res.body[0].hoursViewShowVoList.map((item, index) => {
                    return index >= this.dividedNumber - 1 ? parseFloat(item.planHours) : '';
                });
                option = getResoureLoadChartByMonth(
                    this,
                    this.dividedNumber,
                    actualHourList,
                    planHourList,
                    getDetailApi,
                    wholeList
                );
            }
            this.$nextTick(() => {
                myChart.setOption(option);
            });
            window.addEventListener('resize', () => {
                myChart.resize();
            });
        },
        /**
         * 获取每一列的样式
         * @param {Number} index 序号
         * @returns {String} 样式
         */
        getHeaderClassName(index) {
            // 只有汇总这一列，就不展示节假日
            if (this.allData.length <= 1 || this.view === 'month') return '';
            return this.allData[0]?.hoursViewShowVoList[index]?.workDay ? '' : 'rest-day';
        },
        /**
         * 异常工时对应的样式(只有日视图生效)
         * @param {Number} index 索引
         * @returns {String} 样式
         */
        abnormalWorkHoursClass(index) {
            if (this.view === 'month' || this.allData.length === 0) {
                return '';
            }
            // 第一行的数据
            const curData = this.allData[0]?.hoursViewShowVoList[index];
            const map = {
                正常: '',
                请假: 'on-leave',
                出差: 'on-business'
            };
            const className = map[curData?.workStatus || '正常'];
            // 节假日不显示请假
            if (!curData.workDay && className === 'on-leave') return '';
            return className;
        },
        // 休假与出差的提示语
        computedTooltip(index) {
            const map = {
                '': '',
                'on-leave': '请假',
                'on-business': '出差'
            };
            return map[this.abnormalWorkHoursClass(index)];
        }
    }
};
</script>

<style lang="scss" scoped>
.fz-12 {
    font-size: 12px;
}
.resource-load {
    width: 100%;
    height: 100%;
}
.resourceChart {
    margin: 50px 20px;
    border: 1px solid;
    border-radius: 10px;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
    height: 500px;
    overflow: hidden;
}
.tabGroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .filter {
        flex: 1;
        display: flex;
        justify-content: center;
    }
}
.select-icon {
    height: 15px;
    width: 15px;
}

@mixin status-info($bg-color) {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        margin-right: 5px;
        border-radius: 50%;
        background-color: $bg-color;
    }
}
.status-info--low {
    @include status-info(#13ce66);
}
.status-info--mid {
    @include status-info(#ffba00);
}
.status-info--high {
    @include status-info(#ff4949);
}
// 突出显示当天/当月
::v-deep .curDate {
    height: 30px;
    width: 30px !important;
    background-color: rgb(235, 58, 19);
    display: grid;
    place-items: center;
    text-align: center;
    border-radius: 50%;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    line-height: 30px;
}
// echart中加载任务详情时的骨架屏样式
::v-deep .skeleton {
    background: linear-gradient(45deg, #f6f7f8 0%, #edeef1 10%, #f6f7f8 20%, #f6f7f8 100%);
    background-size: 200% 100%;
    animation: flow 1s linear infinite;
}
@keyframes flow {
    0% {
        background-position: 50% 0;
    }

    100% {
        background-position: -150% 0;
    }
}
@mixin work-status($status) {
    &::after {
        content: $status;
        position: absolute;
        top: 1%;
        right: 1px;
        line-height: 1;
        color: #4a148c;
        background-color: rgba(255, 255, 255, 0.7);
        padding: 3px;
        border-radius: 50rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        font-weight: 500;
    }
    &:hover::after {
        transform: scale(1.2);
        cursor: pointer;
    }
    &::after::after {
        content: attr(title);
    }
}

.on-leave {
    @include work-status('假');
    font-size: 10px;
    @include respond-to('xl') {
        font-size: 9px;
    }
}
.on-business {
    @include work-status('差');
    font-size: 10px;
    @include respond-to('xl') {
        font-size: 9px;
    }
}
// 修改默认的行块盒样式
::v-deep th .cell {
    height: 45px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: relative;
}
// 统一表头高度，修正固定列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}
::v-deep .el-table--mini .el-table__cell {
    padding: 3px 0 !important;
}

::v-deep td:not(.task-type) .cell {
    display: flex;
    justify-content: center;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
::v-deep .rest-day:not(th) .cell {
    background-color: rgb(245, 245, 245, 0.4);
}
::v-deep .el-table__cell.rest-day {
    background-color: rgb(245, 245, 245, 0.4);
}
// 去除斑马纹
::v-deep .el-table tr.el-table__row:nth-child(even) {
    background-color: #fff !important;
}
</style>
