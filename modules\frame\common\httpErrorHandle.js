import { Message } from 'element-ui';
import Cookies from 'js-cookie';
import { getEnvFlag } from '@/tools';
// 用于处理4500毫秒内，只进行一次弹窗处理
let msgFlag = false;
/**
 * msgTips
 * @param {*} status status
 * @param {*} i18n i18n
 * @param {*} router router
 * @param {*} store store
 */
function msgTips(status, i18n, router, store) {
    if (!msgFlag) {
        msgFlag = true;
        Message.error({
            message: i18n.t(`httpCode.http${status}`)
        });
        setTimeout(() => {
            msgFlag = false;
        }, 4500);

        if (status === 401) {
            // 清除token
            Cookies.set('token', '');
            const evnFlag = getEnvFlag();
            const loginPageUrl = `https://${evnFlag}adm.xinbeiyang.info/#/login/index`;
            window.location.href = loginPageUrl;
        }
    }
}

/**
 * 根据错误状态码，跳转到对应的错误界面
 * @param {*} status status
 * @param {*} i18n i18n
 * @param {*} router router
 * @param {*} store store
 */
function redirectErrorPage(status, i18n, router, store) {
    // 判断当前是否二级路由
    const isSubRouter = router.currentRoute.path.split('/').filter((v) => !!v).length >= 2;
    if (isSubRouter) {
        //  删除缓存数组中最后一个
        const delRoute = store.state.tagsView.cachedViews.pop();
        store.dispatch('tagsView/delCachedView', delRoute);
        router.push(`/error/${status}`);
    } else {
        // 一级路由情况先不考虑，router.push({ name: `${status}`, params: { from: 'login' }});
    }
}

/*
公用请求的方法
返回数据，路由
*/
export default (response, i18n, router, store) => {
    let flag = null;
    if (response && response.status) {
        if (response.status === 200) {
            flag = true;
        } else if (response.status === 401) {
            // 这里关闭其他消息，避免消息雪崩
            Message.closeAll();
            msgTips(response.status, i18n, router, store);
        } else if (response.status === 404 || response.status === 500 || response.status === 403) {
            Message.closeAll();
            redirectErrorPage(response.status, i18n, router, store);
        } else {
            flag = true;
        }
    }
    return flag;
};
