// 此js用于更新未读消息数
export default {
    computed: {
        unreadCount() {
            return this.$store.state.systemManagement.unreadCount;
        }
    },
    methods: {
        // 获取未读消息数
        getUnreadCount() {
            this.$service.systemManagement.getNoReadMessage({}).then((res) => {
                if (res.code === '00') {
                    this.$store.commit('systemManagement/updateUnreadCount', res.result);
                }
            });
        }
    }
};
