<template>
    <div>
        <!-- 按钮 -->
        <div class="add-btn">
            <el-button type="primary" @click="handleExport()" v-if="this.type !== 'edit'">导入产品</el-button>
            <el-button type="primary" @click="confirm()">发布</el-button>
            <el-button type="info" @click="handleBack()">返回</el-button>
        </div>
        <!-- 页签菜单 -->
        <el-menu :default-active="activeIndex" mode="horizontal" @select="handleTabSelect">
            <el-menu-item index="0">基本信息</el-menu-item>
            <el-menu-item index="1">产品结构</el-menu-item>
        </el-menu>
        <div v-if="activeIndex === '0'" style="padding: 20px">
            <formula-title :title="dynamicTitle"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="140px" :rules="userRules">
                <div class="add-flex">
                    <el-form-item label="产品名称" prop="productName">
                        <el-input
                            v-model="addForm.productName"
                            placeholder="请输入产品名称"
                            clearable
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="产品编号" prop="productCode">
                        <el-input
                            v-model="addForm.productCode"
                            placeholder="请输入产品编号"
                            clearable
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                </div>
                <div class="add-flex">
                    <el-form-item label="产品线" prop="productLine">
                        <el-select v-model="addForm.productLine" placeholder="请选择产品线" clearable>
                            <el-option
                                v-for="item in productLineData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="Product Owner" prop="productOwner">
                        <el-input
                            v-model="addForm.productOwner"
                            placeholder="请输入Product Owner"
                            clearable
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="目标用户" prop="targetUsers">
                    <el-input
                        type="textarea"
                        v-model="addForm.targetUsers"
                        maxlength="500"
                        :rows="4"
                        placeholder="建议参考的模板：<行业/身份>中面临<具体痛点>的<角色>"
                    ></el-input>
                </el-form-item>
                <el-form-item label="产品定位" prop="productPosition">
                    <el-input
                        type="textarea"
                        v-model="addForm.productPosition"
                        placeholder="建议参考的模板：针对<细分市场>的<产品类型>，以<差异化特性>区别于<竞品类别>"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="核心价值" prop="coreValue">
                    <el-input
                        type="textarea"
                        v-model="addForm.coreValue"
                        placeholder="建议参考的模板：功能价值：通过<核心技术/功能>实现<用户获益>，如<使用场景>。情感价值：帮助用户<心理诉求>。量化价值：将<原有指标>从X提升至Y。"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="竞争策略" prop="competitiveStrategy">
                    <el-input
                        type="textarea"
                        v-model="addForm.competitiveStrategy"
                        placeholder="建议参考的模板：差异化路径：在<维度>上超越竞品或保持同等水平（如：<具体指标>）。防御策略：通过<壁垒类型>构建护城河，如<专利/生态等>。替代方案转化：针对<竞品用户>的<迁移诱因>。"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div v-if="activeIndex === '1'" style="padding: 20px">111111111</div>
    </div>
</template>
<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';

export default {
    name: 'AddProduct',
    components: { formulaTitle },
    data() {
        return {
            activeIndex: '0',
            type: '',
            row: {},
            addForm: {
                productName: '',
                productCode: '',
                productLine: '',
                productOwner: '',
                targetUsers: '',
                productPosition: '',
                coreValue: '',
                competitiveStrategy: ''
            },
            productLineData: [],
            userRules: {
                productName: [
                    {
                        required: true,
                        message: '请输入产品名称',
                        trigger: 'blur'
                    }
                ],
                productCode: [
                    {
                        required: true,
                        message: '请输入产品编号',
                        trigger: 'blur'
                    }
                ],
                productLine: [
                    {
                        required: true,
                        message: '请选择产品线',
                        trigger: 'change'
                    }
                ],
                productOwner: [
                    {
                        required: true,
                        message: '请输入Product Owner',
                        trigger: 'blur'
                    }
                ],
                targetUsers: [
                    {
                        required: true,
                        message: '请输入目标用户',
                        trigger: 'blur'
                    }
                ],
                productPosition: [
                    {
                        required: true,
                        message: '请输入产品定位',
                        trigger: 'blur'
                    }
                ],
                coreValue: [
                    {
                        required: true,
                        message: '请输入核心价值',
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    computed: {
        dynamicTitle() {
            if (this.type === 'add') {
                return '新建产品';
            }
            return '编辑产品';
        }
    },
    created() {
        this.type = this.$route.query.type;
        this.row = this.$route.query.row;
        this.queryData();
        this.$nextTick(() => {
            if (this.type === 'add') {
                this.addForm = {
                    productName: '',
                    productCode: '',
                    productLine: '',
                    productOwner: '',
                    targetUsers: '',
                    productPosition: '',
                    coreValue: '',
                    competitiveStrategy: ''
                };
            }
            if (this.type === 'edit') {
                this.addForm = { ...this.row };
            }

            if (this.$refs.dataForm) {
                this.$refs.dataForm.resetFields();
            }
        });
    },
    methods: {
        async handleTabSelect(index) {
            if (index === '1') {
                const valid = await this.validateForm();
                if (!valid) return;
            }
            this.activeIndex = index;
        },
        // 查询产品线
        async queryData() {
            try {
                const res = await this.$service.dms.product.getProductLine();
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    return;
                }
                this.productLineData = res.data.map((item) => {
                    return {
                        value: item,
                        label: item
                    };
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 返回上一页
        handleBack() {
            this.$router.back();
        },
        // 发布按钮点击事件：收集所有子组件数据
        async confirm() {
            const valid = await this.validateForm();
            if (!valid) return;
            try {
                const params = this.addForm;
                if (this.type === 'add') {
                    params.id = 1;
                }
                // 4. 调用后端接口提交数据
                const res =
                    this.type === 'add'
                        ? await this.$service.dms.product.addProduct(params)
                        : await this.$service.dms.product.editProduct(params);

                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.$router.back();
                } else {
                    this.$message.error(res.message || '发布失败，请重试');
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },

        async validateForm() {
            return new Promise((resolve) => {
                this.$refs.dataForm.validate((valid) => {
                    resolve(valid);
                });
            });
        },
        // 导入
        handleExport() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx';
            input.multiple = false;
            input.style.display = 'none';
            const handleChange = (event) => {
                const file = event.target.files[0];
                if (!file) {
                    return;
                }
                // 参数
                const formData = new FormData();
                formData.append('file', file);
                const exportassDisList = this.$service.dms.product.importProduct;
                exportassDisList(formData).then(async (res) => {
                    if (res.code === '0000') {
                        this.$tools.message.suc(res.message);
                        this.addForm = res.data;
                    } else {
                        this.$tools.message.err(res.message || '系统异常');
                    }
                });
            };
            input.addEventListener('change', handleChange);
            input.click();
        }
    }
};
</script>
<style lang="scss" scoped>
.add-flex {
    width: 100%;
    display: flex;
    justify-content: flex-start;
}
.add-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
::v-deep .el-menu.el-menu--horizontal {
    display: flex;
    justify-content: flex-end;
    margin: 1vh 0;
    border-bottom: 3px solid #3370ff;
    .el-menu-item {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
        color: #4377ee;
        height: 45px;
        line-height: 45px;
    }
    .el-menu-item.is-active {
        background-color: #4377ee;
        color: #fff;
    }
}
</style>
