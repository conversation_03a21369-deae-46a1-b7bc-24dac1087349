// 这里导入是为了在接口开始、结束请求，进行loading提示，避免重复点击按钮提交
// import { Loading, Message } from 'element-ui';
import { Loading, Message } from 'element-ui';
import i18n from 'wtf-core-vue-ng/src/lang';

export default (Vue, http) => {
    // 全局的消息组件，用于接口异常提示
    const msg = Vue.prototype.$message;

    // 请求加载loading
    let loadingInstance;
    // 当前请求的config信息
    let currConfig = {};
    // 添加请求拦截器
    http.interceptors.request.use(
        (config) => {
            //  如果请求的接口不在配置的黑名单中，则添加version
            currConfig = config;
            // 判断接口是否需要加载提示，没有配置默认为提示
            if (config.loading === false) {
                return config;
            }
            // 每次接口请求，进行loading提示
            loadingInstance = Loading.service({
                text: i18n.t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            return config;
        },
        (error) => {
            // 对请求错误做些什么
            loadingInstance && loadingInstance.close();
            return Promise.reject(error);
        }
    );
    // 添加响应拦截器，用于公共接口响应处理，包括frame模块
    http.interceptors.response.use(
        (response) => {
            // 成功响应，关闭loading
            loadingInstance && loadingInstance.close();
            return response;
        },
        (error) => {
            // 接口响应异常，也要关闭loading
            loadingInstance && loadingInstance.close();
            // 接口没有权限，这里暂时只进行提示，相关无权限功能，待下个版本开发
            const respStatus = error.response && error.response.status;
            // 有页面跳转的，则不提示错误消息
            if ([401, 403, 500, 404].every((v) => v !== respStatus)) {
                // 这里关闭其他消息，避免消息雪崩
                Message.closeAll();
                if (currConfig.shwoErrMessage !== false) {
                    msg.error('接口请求失败！');
                }
            }
            return Promise.reject(error);
        }
    );
};
