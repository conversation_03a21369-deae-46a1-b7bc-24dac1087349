<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="loadProductData"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 230px)' }"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @selection-change="handleSelectionChange"
                @pagination="handlePagination"
            >
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <!-- 已拒绝的才能编辑 ,同时配置角色权限-->
                    <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                    <!-- 待审核的才能审核 ,同时配置角色权限-->
                    <el-button type="text" size="small" @click="handleAudit(row)">审核</el-button>
                </template>
            </project-list>
        </div>
        <TemporaryProjectDetailDialog
            :type="viewType"
            :visible.sync="temporaryProjectDetailDialogVisible"
            :projectId="currentProjectId"
        />
        <TemporaryProjectDialog
            :visible.sync="temporaryProjectDialogVisible"
            :projectId="currentProjectId"
            :type="editType"
            @success="handleTemporaryProjectSuccess"
        />
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import TemporaryProjectDetailDialog from 'dms/views/projectManagement/projectList/components/TemporaryProjectDetailDialog.vue';
import TemporaryProjectDialog from 'dms/views/projectManagement/projectList/components/TemporaryProjectDialog.vue';

export default {
    name: 'ProjectManagementList',
    components: {
        ProjectList,
        TemporaryProjectDetailDialog,
        TemporaryProjectDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            // TODO: 改回去
            // productData: [],
            productData: [{}, {}, {}, {}, {}],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10,
            // 新增/编辑
            editType: 'add',
            // 查看/审核
            viewType: 'view',
            // 新增/编辑临时项目弹窗
            temporaryProjectDialogVisible: false,
            // 查看临时项目详情弹窗
            temporaryProjectDetailDialogVisible: false
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams
                };

                // 日期处理
                if (params.foundDate && params.foundDate.length > 0) {
                    params.startDateString = params.foundDate[0];
                    params.endDateString = params.foundDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closeStartDateString = params.closeDate[0];
                    params.closeEndDateString = params.closeDate[1];
                }

                const response = await this.$service.dms.common.getProjectOrGroupList(params);

                if (response.code === '000000') {
                    this.productData = response.result?.list || [];
                    this.total = response.result?.total || 0;
                } else {
                    this.$message.error(response.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理重置
        handleReset() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的产品:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },

        // 编辑产品
        handleEdit(row) {
            this.editType = 'edit';
            this.currentProjectId = row.projectId;
            this.temporaryProjectDialogVisible = true;
        },

        // 查看产品详情
        handleView(row) {
            this.viewType='view';
            this.currentProjectId = row.projectId;
            this.temporaryProjectDetailDialogVisible = true;
        },

        // 审核产品
        handleAudit(row) {
            this.viewType='audit';
            this.currentProjectId = row.projectId;
            this.temporaryProjectDetailDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
