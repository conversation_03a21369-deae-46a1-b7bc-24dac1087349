<template>
    <div class="view" style="height: calc(100vh - 55px)">
        <div class="container">
            <h1>{{ title }}</h1>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DashboardIndex',
    data() {
        return {};
    },
    computed: {
        title() {
            return this.$t(this.$store.state.settings.title);
        }
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
