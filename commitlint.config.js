/* eslint-disable no-inline-comments */
/* eslint-disable line-comment-position */
module.exports = {
    extends: ['@commitlint/config-conventional'],
    rules: {
        // commit 的内容，只允许使用下面9个标识
        'type-enum': [
            2,
            'always',
            [
                'feature', // 功能添加
                'bugfix', // bug修复
                'optimize', // 优化过程
                'doc', // 文档变更
                'refactor', // 重构，功能不变
                'test', // 测试代码的调整
                'merge', // 分支合并
                'delete', // 分支删除
                'change' // 调整，比如配置，某些方法替换等
            ]
        ],
        'type-empty': [2, 'never'],
        'type-case': [2, 'always', 'lower-case'],
        'subject-full-stop': [0, 'never'],
        'subject-case': [0, 'never']
    }
};
