<template>
    <table>
        <tbody>
            <tr>
                <td
                    :colspan="datasource.children && datasource.children.length ? datasource.children.length * 2 : null"
                >
                    <div class="node" :id="datasource.id" @click.stop="handleClick(datasource)">
                        <slot :node-data="datasource">
                            <div
                                class="title"
                                :class="{
                                    'dual-half-border':
                                        datasource.topHalfBorderColor || datasource.bottomHalfBorderColor
                                }"
                                :style="titleStyle"
                            >
                                <!-- 右边框元素 -->
                                <div
                                    v-if="datasource.topHalfBorderColor || datasource.bottomHalfBorderColor"
                                    class="right-border"
                                    :style="rightBorderStyle"
                                ></div>

                                <i
                                    v-if="datasource.showIcon"
                                    class="fa fa-solid fa-user symbol"
                                    :style="{
                                        color: datasource.iconColor || 'inherit'
                                    }"
                                ></i>

                                <span :style="nameStyle">
                                    <span>{{ datasource.name }}</span>
                                </span>
                                <div class="actions">
                                    <template v-if="actionsCount > 1">
                                        <el-popover
                                            placement="bottom"
                                            trigger="click"
                                            width="auto"
                                            popper-class="organization-chart-popover"
                                        >
                                            <div class="popover-actions">
                                                <el-button
                                                    v-if="datasource.canAdd"
                                                    class="add-button"
                                                    type="text"
                                                    size="mini"
                                                    icon="el-icon-circle-plus-outline"
                                                    :style="{
                                                        color: datasource.addBtnColor || '#fff'
                                                    }"
                                                    @click.stop="handleAdd(datasource)"
                                                >
                                                </el-button>
                                                <el-button
                                                    v-if="datasource.canEdit"
                                                    class="edit-button"
                                                    type="text"
                                                    size="mini"
                                                    icon="el-icon-edit"
                                                    :style="{
                                                        color: datasource.editBtnColor || '#fff'
                                                    }"
                                                    @click.stop="handleEdit(datasource)"
                                                >
                                                </el-button>
                                                <el-button
                                                    v-if="datasource.canDelete"
                                                    class="delete-button"
                                                    type="text"
                                                    size="mini"
                                                    icon="el-icon-delete"
                                                    @click.stop="handleDelete(datasource)"
                                                >
                                                </el-button>
                                            </div>
                                            <el-button
                                                slot="reference"
                                                class="settings-button"
                                                type="text"
                                                size="mini"
                                                icon="el-icon-setting"
                                                @click.stop
                                            ></el-button>
                                        </el-popover>
                                    </template>
                                    <template v-else-if="actionsCount === 1">
                                        <el-button
                                            v-if="datasource.canAdd"
                                            class="add-button"
                                            type="text"
                                            size="mini"
                                            icon="el-icon-circle-plus-outline"
                                            :style="{
                                                color: datasource.addBtnColor || '#fff'
                                            }"
                                            @click.stop="handleAdd(datasource)"
                                        >
                                        </el-button>
                                        <el-button
                                            v-if="datasource.canEdit"
                                            class="edit-button"
                                            type="text"
                                            size="mini"
                                            icon="el-icon-edit"
                                            :style="{
                                                color: datasource.editBtnColor || '#fff'
                                            }"
                                            @click.stop="handleEdit(datasource)"
                                        >
                                        </el-button>
                                        <el-button
                                            v-if="datasource.canDelete"
                                            class="delete-button"
                                            type="text"
                                            size="mini"
                                            icon="el-icon-delete"
                                            @click.stop="handleDelete(datasource)"
                                        >
                                        </el-button>
                                    </template>
                                </div>
                            </div>
                        </slot>
                        <div
                            v-if="datasource.children && datasource.children.length"
                            class="edge verticalEdge bottomEdge"
                            @click.stop="toggle"
                        >
                            <i :class="['fa fa-solid', collapsed ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
                        </div>
                    </div>
                </td>
            </tr>
            <template v-if="datasource.children && datasource.children.length && !collapsed">
                <tr class="lines">
                    <td v-if="datasource.children.length > 1" :colspan="datasource.children.length * 2">
                        <div class="downLine"></div>
                    </td>
                </tr>
                <tr class="lines" v-if="datasource.children.length > 1">
                    <td class="rightLine"></td>
                    <template v-for="n in datasource.children.length - 1">
                        <td class="leftLine topLine" :key="'left' + n"></td>
                        <td class="rightLine topLine" :key="'right' + n"></td>
                    </template>
                    <td class="leftLine"></td>
                </tr>
                <tr class="lines" v-else style="height: 20px">
                    <td class="rightLine"></td>
                    <td class="leftLine"></td>
                </tr>
                <tr class="nodes">
                    <td colspan="2" v-for="child in datasource.children" :key="child.id">
                        <node
                            :datasource="child"
                            @handle-click="handleClick"
                            @handle-add="handleAdd"
                            @handle-edit="handleEdit"
                            @handle-delete="handleDelete"
                            :default-collapsed="defaultCollapsed"
                        >
                            <template v-for="slot in Object.keys($scopedSlots)" :slot="slot" slot-scope="scope">
                                <slot :name="slot" v-bind="scope" />
                            </template>
                        </node>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
export default {
    name: 'Node',
    props: {
        datasource: Object,
        defaultCollapsed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            collapsed: this.datasource.collapsed === undefined ? this.defaultCollapsed : this.datasource.collapsed
        };
    },
    computed: {
        actionsCount() {
            return (
                (this.datasource.canAdd ? 1 : 0) +
                (this.datasource.canEdit ? 1 : 0) +
                (this.datasource.canDelete ? 1 : 0)
            );
        },
        titleStyle() {
            const style = {
                'background-color': this.datasource.backgroundColor || 'rgba(217, 83, 79, 0.8)',
                'color': this.datasource.color || '#fff',
                'width': 'fit-content',
                'white-space': 'nowrap'
            };

            const borderWidth = this.datasource.borderWidth || '2px';

            // 设置CSS变量用于伪元素
            style['--border-width'] = borderWidth;

            // 处理双半边框配置
            if (this.datasource.topHalfBorderColor || this.datasource.bottomHalfBorderColor) {
                // 设置上半边框和下半边框的颜色变量
                if (this.datasource.topHalfBorderColor) {
                    style['--top-half-border-color'] = this.datasource.topHalfBorderColor;
                }
                if (this.datasource.bottomHalfBorderColor) {
                    style['--bottom-half-border-color'] = this.datasource.bottomHalfBorderColor;
                }
            } else if (this.datasource.borderColor) {
                // 传统的单一边框配置
                style.border = `${borderWidth} solid ${this.datasource.borderColor}`;
            }

            return style;
        },
        rightBorderStyle() {
            const borderWidth = this.datasource.borderWidth || '2px';
            return {
                '--border-width': borderWidth,
                '--top-half-border-color': this.datasource.topHalfBorderColor || 'transparent',
                '--bottom-half-border-color': this.datasource.bottomHalfBorderColor || 'transparent'
            };
        },
        nameStyle() {
            const style = {
                'display': 'inline-block',
                'text-align': 'center'
            };
            // 图标宽度
            const iconWidth = 26;
            // 按钮宽度
            const buttonWidth = 20;
            // 靠左边的宽度
            let spaceLeft = 8;
            if (this.datasource.showIcon) {
                spaceLeft = iconWidth;
            }
            // 靠右边的宽度
            let spaceRight = 8;
            // 按钮数量
            let actionsCount = 0;
            if (this.datasource.canAdd || this.datasource.canEdit || this.datasource.canDelete) {
                actionsCount = 1;
            }
            // 按钮宽度
            if (actionsCount > 0) {
                spaceRight = actionsCount * buttonWidth;
            }

            style.padding = `0 ${spaceRight}px 0 ${spaceLeft}px`;

            return style;
        }
    },
    methods: {
        toggle() {
            this.collapsed = !this.collapsed;
        },
        handleAdd(nodeData) {
            this.$emit('handle-add', nodeData);
        },
        handleDelete(nodeData) {
            this.$emit('handle-delete', nodeData);
        },
        handleClick(nodeData) {
            this.$emit('handle-click', nodeData);
        },
        handleEdit(nodeData) {
            this.$emit('handle-edit', nodeData);
        }
    }
};
</script>

<style lang="scss" scoped>
.title {
    position: relative;

    // 双半边框样式 - 上半边框和下半边框同时存在但颜色不同
    &.dual-half-border {
        position: relative;

        // 上边框
        border-top: var(--border-width, 2px) solid var(--top-half-border-color, transparent);

        // 下边框
        border-bottom: var(--border-width, 2px) solid var(--bottom-half-border-color, transparent);

        // 左边框上半部分
        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: var(--border-width, 2px);
            height: 50%;
            background-color: var(--top-half-border-color, transparent);
            z-index: 1;
        }

        // 左边框下半部分
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: var(--border-width, 2px);
            height: 50%;
            background-color: var(--bottom-half-border-color, transparent);
            z-index: 1;
        }
    }

    // 右边框元素样式
    .right-border {
        position: absolute;
        right: 0;
        top: 0;
        width: var(--border-width, 2px);
        height: 100%;
        z-index: 1;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background-color: var(--top-half-border-color, transparent);
        }

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background-color: var(--bottom-half-border-color, transparent);
        }
    }
}
.symbol {
    position: absolute;
    left: 0;
    top: 0;
}
.actions {
    position: absolute;
    top: 0;
    right: 2px;
    display: flex;
}
.popover-actions .el-button {
    // margin: 0 2px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.settings-button {
    color: #fff;
    &:hover {
        transform: scale(1.2);
    }
}
.add-button {
    &:hover {
        transform: scale(1.2);
    }
}
.edit-button {
    margin-left: 2px;
    &:hover {
        transform: scale(1.2);
    }
}
.delete-button {
    color: #f56c6c;
    margin-left: 2px;
    &:hover {
        transform: scale(1.2);
    }
}
</style>
<style>
.organization-chart-popover.el-popover {
    min-width: 45px;
}
</style>
