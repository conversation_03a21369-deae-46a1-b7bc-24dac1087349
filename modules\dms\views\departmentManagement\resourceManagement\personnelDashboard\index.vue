<template>
    <div style="padding: 8px 20px 20px 20px; position: relative">
        <el-tabs v-model="activeName">
            <el-tab-pane label="个人负载" name="personalLoad" lazy v-if="showPersonalLoad">
                <PersonResourceLoad
                    :loginName="loginName"
                    :userName="userName"
                    :date="date"
                    :inputView="view"
                ></PersonResourceLoad>
            </el-tab-pane>
        </el-tabs>
        <el-button type="primary" @click="goBack" style="position: absolute; right: 20px; top: 14px">返回</el-button>
    </div>
</template>

<script>
import PersonResourceLoad from 'dms/views/departmentManagement/components/personnelResource';

export default {
    components: {
        PersonResourceLoad
    },
    data() {
        return {
            // 当前页签的位置
            activeName: 'dashBoard',
            // 域账号
            loginName: '',
            // 姓名
            userName: '',
            // 选择的日期
            date: '',
            // 视图
            view: ''
        };
    },
    computed: {},
    watch: {},
    created() {
        // 需要查看资源负载时
        if (this.$route?.query?.name) {
            this.activeName = 'personalLoad';
            this.loginName = this.$route?.query?.name;
            this.date = this.$route?.query?.date;
            this.view = this.$route?.query?.view;
            this.userName = this.$store.state.dms.resourceName;
            this.showPersonalLoad = true;
        }
    },
    methods: {
        goBack() {
            history.go(-1);
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex {
    display: flex;
}
</style>
