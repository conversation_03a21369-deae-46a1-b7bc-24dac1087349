<template>
    <div class="view">
        <project-list
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :columns="columns"
            :data="productData"
            :total="total"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @pagination="handlePagination"
            :table-attrs="{ height: 'calc(100vh - 230px)' }"
        >
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                <el-button type="text" size="small" @click="handleAudit(row)">变更</el-button>
                <el-button type="text" size="small" @click="handleStop(row)">暂停</el-button>
                <el-button type="text" size="small" style="color: #f56c6c" @click="handleClose(row)">关闭</el-button>
            </template>
        </project-list>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';

export default {
    name: 'ChangeList',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '所有',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            currentPage: 1,
            pageSize: 10
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    currentPage: this.currentPage,
                    pageSize: this.pageSize
                };
                if (this.activeNavTab === '所有') {
                    params.status = '';
                } else {
                    params.status = this.activeNavTab;
                }
                // 日期处理
                if (params.createDate && params.createDate.length > 0) {
                    params.createStartDate = params.createDate[0];
                    params.createEndDate = params.createDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closedStartDate = params.closeDate[0];
                    params.closedEndDate = params.closeDate[1];
                }
                const res = await this.$service.dms.product.getProductList(params);
                if (res.code === '0000') {
                    this.productData = res.data.list || [];
                    this.total = res.data.total || 0;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },
        // 处理搜索
        handleSearch() {
            this.currentPage = 1;
            this.loadProductData();
        },
        // 处理重置
        handleReset() {
            this.currentPage = 1;
            this.loadProductData();
        },
        // 处理导航切换
        handleNavChange() {
            this.loadProductData();
        },
        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },
        // 查看详情
        handleDetails(row) {
            this.$router.push({ name: 'DetailsProduct', query: { id: row.id } });
        },
        // 暂停
        handleStop(row) {
            this.$confirm('确定暂停该产品?', '暂停', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        productId: row.id,
                        type: '已暂停'
                    };
                    this.$service.dms.product.closedProduct(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success('暂停成功!');
                            this.loadProductData();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消该操作');
                });
        },
        // 关闭
        async handleClose(row) {
            this.$confirm('确定关闭该产品?', '关闭', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        productId: row.id,
                        type: '已关闭'
                    };
                    this.$service.dms.product.closedProduct(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success('关闭成功!');
                            this.loadProductData();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消该操作');
                });
        }
    }
};
</script>

<style lang="scss" scoped></style>
