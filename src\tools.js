/* eslint-disable valid-jsdoc */
import Vue from 'vue';
import Tools from 'wtf-core-vue-ng/src/utils/index';
import { MessageBox, Message, Notification, Loading } from 'element-ui';
import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import J<PERSON>Zip from 'jszip';
import FileSaver from 'file-saver';

/**
 * 获取一个标志字符串，区分环境
 *
 * @returns {Object} env
 */
export function getEnvFlag() {
    const { hostname } = window.location;
    const envConst = hostname.split('.')[0];
    let envStr = '';
    if (envConst.startsWith('dev') || envConst.startsWith('local')) {
        envStr = 'dev';
    }
    if (envConst.startsWith('test')) {
        envStr = 'test';
    }
    if (envConst.startsWith('pre')) {
        envStr = 'pre';
    }
    return envStr;
}

/**
 * 确认提示
 *
 * @param {String} msg 确认消息
 * @param {String} title  标题
 * @param {Object} options MessageBox配置项
 * @returns {Promise} promise
 */
function confirm(msg = '确认本次操作？', title = '提示', options = {}) {
    const opt = {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        ...options
    };
    return new Promise((resolve, reject) => {
        MessageBox.confirm(msg, title, opt)
            .then(() => {
                resolve();
            })
            .catch((action) => {
                reject(action);
            });
    });
}

/**
 * 提交内容
 *
 * @param {String} msg 提示
 * @param {String} title  标题
 * @param {Object} options MessageBox配置项
 * @returns {Promise} promise
 */
function prompt(msg = '请输入', title = '提示', options = {}) {
    const opt = {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: msg,
        ...options
    };
    return new Promise((resolve, reject) => {
        MessageBox.prompt(msg, title, opt)
            .then(({ value }) => {
                resolve(value);
            })
            .catch(() => {
                reject(new Error('cancel'));
            });
    });
}

/**
 * 消息提示
 *
 * @param {*} content 消息内容
 */
function message(content) {
    Message.closeAll();
    Message.info(content);
}

message.suc = (content) => {
    Message.closeAll();
    Message.success(content);
};

message.err = (content) => {
    Message.closeAll();
    Message.error(content);
};

message.warning = (content) => {
    Message.closeAll();
    Message.warning(content);
};

/**
 * 通知
 * @param {*} msg 通知内容
 * @param {*} options 配置
 */
function notify(msg, options) {
    Notification({
        ...options,
        title: options.title || '消息',
        message: msg
    });
}

notify.suc = (msg, options) => {
    Notification({
        ...options,
        title: options.title || '成功',
        type: 'success',
        message: msg
    });
};

notify.err = (msg, options) => {
    Notification({
        ...options,
        title: options.title || '错误',
        type: 'error',
        message: msg
    });
};

notify.warn = (msg, options) => {
    Notification({
        ...options,
        title: options.title || '警告',
        type: 'warn',
        message: msg
    });
};

/**
 * 表单校验
 *
 * @param {*} formRef 表单实例
 * @returns {Promise} promise
 */
function validateForm(formRef) {
    return new Promise((resolve, reject) => {
        formRef.validate((valid) => {
            if (valid) {
                resolve(valid);
            } else {
                reject(valid);
            }
        });
    });
}

/**
 * 表单单个字段校验
 *
 * @param {*} formRef 表单实例
 * @param {String} prop 表单字段key
 * @returns {Promise} promise
 */
function validateField(formRef, prop) {
    return new Promise((resolve, reject) => {
        formRef.validateField(prop, (error) => {
            if (error) {
                reject(error);
                return false;
            }
            resolve(`${prop}校验通过`);
        });
    });
}

/**
 * Loading显示
 */
function showLoading() {
    Loading.service({
        text: '正在处理，请稍候...'
    });
}
/**
 * Loading隐藏
 */
function hideLoading() {
    Loading.service().close();
}

/**
 * 根据选项值匹配选项名称
 * @param {String|Number} value 选项值
 * @param {Array} options 选项数组
 * @returns {String} 选项名称
 */
function getOptionName(value, options = []) {
    const target = options.find((option) => option.value === value);
    return target ? target.label : '未知';
}
/**
 *
 * @param {Object} datePickerItem 日期组件定义对象
 * @param {Object} targetObj 操作对象，一般为查询条件对象
 * @param {String} dateKey 开始日期或结束日期对应字段名
 * @param {String} datePickerItemType start | end，操作的日期组件是开始日期还是结束日期
 * @returns {Object} 返回datePickerItem本身
 */
function setDatePickerDisabledValue(datePickerItem, targetObj, dateKey, datePickerItemType = 'start') {
    if (!datePickerItem.elDatePickerAttrs) {
        datePickerItem.elDatePickerAttrs = {};
    }
    if (!datePickerItem.elDatePickerAttrs.pickerOptions) {
        datePickerItem.elDatePickerAttrs.pickerOptions = {};
    }
    const { pickerOptions } = datePickerItem.elDatePickerAttrs;
    if (datePickerItemType === 'start') {
        pickerOptions.disabledDate = (date) => {
            if (targetObj[dateKey]) {
                return date.getTime() > new Date(targetObj[dateKey]).getTime();
            }
        };
    }
    if (datePickerItemType === 'end') {
        pickerOptions.disabledDate = (date) => {
            if (targetObj[dateKey]) {
                return date.getTime() < new Date(targetObj[dateKey]).getTime();
            }
        };
    }
    return datePickerItem;
}

/**
 * @func downloadExprotFile
 * @desc 根据文件流下载文件
 * @param {string} fileStream 文件流
 * @param {string} name 文件名
 * @param {string} extension 文件后缀
 * @param {string} [type] 文件类型（当不知道类型时可以选择不写）
 * @returns {object} undefined
 * @example
 */
function downloadExprotFile(fileStream, name, extension, type = '') {
    return new Promise((resolve, reject) => {
        if (typeof fileStream.type === 'string' && fileStream.type.includes('json')) {
            const reader = new FileReader();
            reader.onload = (event) => {
                const msg = JSON.parse(reader.result).message || '系统异常, 导出失败';
                reject(msg);
            };
            reader.readAsText(fileStream);
        } else {
            const blob = new Blob([fileStream], {
                type: type || fileStream.type
            });
            const fileName = `${name}.${extension}`;
            const elink = document.createElement('a');
            elink.download = fileName;
            elink.style.display = 'none';
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href);
            document.body.removeChild(elink);
            resolve(fileStream);
        }
    });
}
/**
 * 将X轴标签文字每3个字符换行，超过4行...展示
 * @param {String} value 坐标轴文字
 * @returns {String} 格式化后的文字
 */
function formatAxisLabel(value = '') {
    return value
        .split('')
        .map((item, index) => {
            if (index > 12) {
                return '';
            } else if (index === 12) {
                return '...';
            }
            return (index + 1) % 3 === 0 ? `${item}\n` : item;
        })
        .join('');
}

/**
 * 格式化开始日期，补00:00:00
 *
 * @param {String} startDate 开始日期
 * @returns {String} 开始日期
 */
function formatStartDate(startDate) {
    if (startDate) {
        return moment(startDate).format('YYYY-MM-DD 00:00:00');
    }
    return '';
}

/**
 * 格式化结束日期，补23:59:59
 *
 * @param {String} endDate 结束日期
 * @returns {String} 结束日期
 */
function formatEndDate(endDate) {
    if (endDate) {
        return moment(endDate).format('YYYY-MM-DD 23:59:59');
    }
    return '';
}
/**
 * el-descriptions组件中，为数组中的项目设置具体值
 *
 * @param {Arrat} list 操作数据
 * @param {Object} object 数据
 */
function setIndicator(list, object) {
    list.forEach((item) => {
        Vue.set(item, 'value', object[item.name] || '');
    });
}

/**
 * 为el-descriptions-item设置样式
 *
 * @param {String} label 字段名
 * @returns {Object} 样式属性
 */
function elDescItemLabelAttrs(label) {
    return {
        label,
        labelStyle: {
            'width': `calc(22px + ${label.length}em)`,
            'color': '#000000',
            'font-style': 'italic'
        },
        contentStyle: {
            'font-weight': 'bold',
            'color': '#000000'
        }
    };
}

/**
 * 比较值返回css样式类
 *
 * @param {Number} value1 值1
 * @param {Number} value2 值2
 * @returns {String} 样式类
 */
function getClassByCompareValue(value1, value2) {
    if (value1 && value2) {
        if (+value1 > +value2) {
            return 'snbc-text-success';
        } else if (+value1 < +value2) {
            return 'snbc-text-danger';
        }
        return '';
    }
    return '';
}
/**
 * 页面参数设置
 */
function setQueryParams() {
    const { query } = this.$route;
    const { queryParams } = this.tableConfig;
    Object.keys(queryParams).forEach((key) => {
        if (query[key] !== undefined) {
            queryParams[key] = query[key];
        }
    });
}

/**
 *
 * 文件上传校验
 *
 * @param {Number} max
 * @param {Array} accept
 * @returns {Function}
 */
export function uploadValidate(max, accept) {
    return {
        beforeUpload: (file) => {
            const imgSize = Number(file.size / 1024 / 1024);
            const FileExt = file.name.replace(/.+\./, '');
            if (max && imgSize > max) {
                message.err(`文件大小不能超过${max}MB，请重新上传。`);
                throw new Error('Filesize maximum exceeded');
            }
            if (accept && !accept.includes(FileExt.toLowerCase())) {
                message.err(`文件类型错误`);
                throw new Error('typeError');
            }
        },
        beforeRemove: (file, fileList) => {
            const FileExt = file.name.replace(/.+\./, '');
            const isLimit = file.size / 1024 / 1024 > max;
            const extension = accept && !accept.includes(FileExt.toLowerCase());
            if (extension || isLimit) {
                const i = fileList.indexOf(file);
                fileList.splice(i, 1);
                throw new Error('Filesize maximum exceeded');
            }
        }
    };
}

/**
 * 批量下载文件并且打包成 zip 文件
 *
 * @param {Arrat} list 操作数据
 * @param {Object} object 数据
 */
async function batchDownload(files, zipName) {
    const zip = new JSZip();
    const promises = [];
    files.forEach((file) => {
        const filename = file.split('/').pop();
        const fileUrl = file;

        promises.push(
            fetch(fileUrl, {
                cache: 'no-store'
            })
                .then((response) => {
                    return response.blob();
                })
                .then((blob) => {
                    zip.file(filename, blob);
                })
                .catch((error) => {
                    throw error;
                })
        );
    });

    await Promise.allSettled(promises);

    const content = await zip.generateAsync({ type: 'blob' });
    FileSaver.saveAs(content, `${zipName || 'batch_download'}.zip`);
}

/**
 * 扩展Tools上的工具函数
 */
export function expandTools() {
    Object.assign(Tools.prototype, {
        getEnvFlag,
        confirm,
        prompt,
        message,
        notify,
        cloneDeep,
        validateForm,
        validateField,
        showLoading,
        hideLoading,
        getOptionName,
        setDatePickerDisabledValue,
        downloadExprotFile,
        formatAxisLabel,
        formatStartDate,
        formatEndDate,
        setIndicator,
        elDescItemLabelAttrs,
        getClassByCompareValue,
        setQueryParams,
        uploadValidate,
        batchDownload
    });
}
