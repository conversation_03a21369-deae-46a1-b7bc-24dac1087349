<template>
    <div style="width: 100%; height: 100%">
        <div class="popoverContent">
            <template>
                <el-empty
                    description="暂无数据"
                    :image-size="60"
                    v-if="taskDetail.length === 0"
                ></el-empty>
                <!-- 任务列表 -->
                <div v-for="(item, index) in taskDetail" :key="index">
                    <div class="flex column-direction">
                        <div
                            v-if="handleProjectMerge(index)"
                            class="font-weight-800 taskList ml-6"
                            :class="
                                handleProjectMerge(index) ? 'red-point' : ''
                            "
                        >
                            项目：{{ item.proProjectName }}
                        </div>
                        <div class="flex">
                            <div class="taskList">
                                <div class="task-detail">
                                    <span style="color: #3370ff">任务：</span
                                    >{{ item.taskName }}
                                </div>
                                <div class="task-detail">
                                    计划：{{ item.startDate }} -
                                    {{ item.endDate }}
                                </div>
                            </div>
                            <div class="taskTime">
                                {{ item.taskHours }}
                            </div>
                        </div>
                    </div>
                    <div
                        class="flex"
                        v-if="item.effortInfoList && type === 'day'"
                    >
                        <div class="task-title">日志：</div>
                        <div class="flex column-direction">
                            <div
                                v-for="(
                                    { effortVal, effortHours },
                                    effortInfoListIndex
                                ) in item.effortInfoList"
                                class="work-hour-detail-list"
                                :key="effortInfoListIndex"
                            >
                                <span
                                    v-if="
                                        item.effortInfoList &&
                                        item.effortInfoList.length > 1
                                    "
                                    >{{
                                        CIRCLED_NUMBERS[effortInfoListIndex]
                                    }}</span
                                >
                                <span class="ellipsis">
                                    {{ effortVal }}
                                </span>
                                <span class="work-hour-dialog"
                                    >{{ effortHours }}h</span
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';

export default {
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'ResourceDetail',
    props: {
        data: {
            type: Array,
            default: () => []
        },
        // 日视图/月视图
        type: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            taskDetail: [],
            CIRCLED_NUMBERS: CONSTANTS.CIRCLED_NUMBERS
        };
    },
    mounted() {
        this.taskDetail = this.data;
    },
    methods: {
        /**
         * 处理相同的项目合并，与上一行相同的项目就不显示
         * @param {Number} index 序号
         * @returns {Boolean} 项目名称是否与上一行相同
         */
        handleProjectMerge(index) {
            if (this.taskDetail.length === 0) return false;
            if (index === 0) return true;
            return (
                this.taskDetail[index].proProjectName !==
                this.taskDetail[index - 1].proProjectName
            );
        }
    }
};
</script>

<style scoped>
.flex {
    display: flex;
}
.ml-6 {
    margin-left: 6px;
}
.font-weight-800 {
    font-weight: 800;
}
.task-detail {
    margin: 2px 0 0 5px;
}
.column-direction {
    flex-direction: column;
}
.popoverContent {
    display: flex;
    flex-direction: column;
    padding: 0px;
}
.taskList {
    width: 320px;
    padding-left: 20px;
    position: relative;
    white-space: wrap;
    word-break: break-all;
}
.red-point::before {
    content: '●';
    color: #ec808d;
    font-size: 20px;
    position: absolute;
    left: 0;
    top: -2px;
    line-height: 1;
}
.taskTime {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 40px;
    background-color: #ffff80;
    font-weight: 800;
    font-size: 16px;
    margin: 5px 0 5px 10px;
    line-height: 50px;
}

.clock-flag {
    width: 18px;
    height: 18px;
    margin: 0 5px 0px 3px;
}

.work-hour-detail-list {
    width: 290px;
    white-space: wrap;
}

.work-hour-dialog {
    color: #409eff;
    width: 40px;
    white-space: nowrap;
}
.task-title {
    margin-left: 25px;
    width: fit-content;
}
</style>
