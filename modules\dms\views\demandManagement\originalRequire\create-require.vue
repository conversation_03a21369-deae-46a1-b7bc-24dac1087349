<template>
    <div class="view">
        <div>
            <!-- 标题 -->
            <formula-title :title="getTitleByType()"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="120px" :rules="userRules">
                <el-form-item label="原始需求" prop="type" v-if="this.demandClass === '子原始需求'">
                    <el-select v-model="addForm.type" placeholder="请选择原始需求" clearable>
                        <el-option v-for="item in typeData" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="用户需求" prop="type" v-else>
                    <el-select v-model="addForm.type" placeholder="请选择用户需求" clearable>
                        <el-option v-for="item in typeData" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        type="textarea"
                        v-model="addForm.userScenario"
                        :rows="15"
                        placeholder="复制多条完整需求用例，系统自动识别。"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div class="sprint-btn">
                <el-button plain @click="back()" class="create-btn">返回</el-button>
                <el-button type="primary" @click="handleEnter()" class="create-btn">录入</el-button>
            </div>
        </div>
        <create-require-pop ref="createRef" :title="createTitle"></create-require-pop>
    </div>
</template>

<script>
import formulaTitle from './components/formulaTitle.vue';
import createRequirePop from './components/createRequirePop.vue';

export default {
    components: { formulaTitle, createRequirePop },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            demandClass: '',
            type: '',
            // 标题
            title: '批量创建用户需求',
            addForm: {},
            typeData: [
                { label: '业务需求', value: 1 },
                { label: '产品优化', value: 2 },
                { label: '技术优化', value: 3 },
                { label: '部门建设', value: 4 }
            ],

            userRules: {
                type: [
                    {
                        required: true,
                        message: '请选择原始需求',
                        trigger: 'change'
                    }
                ]
            },
            // 弹窗
            createTitle: '批量创建用户需求'
        };
    },
    mounted() {
        this.demandClass = this.$route.query.demandClass;
        this.type = this.$route.query.type;
        this.addShift();
    },
    methods: {
        getTitleByType() {
            if (this.demandClass === '子原始需求') {
                return '批量创建用户需求';
            }
            return '批量创建产品需求';
        },
        addShift() {
            this.addForm = {};
            this.$nextTick(() => {
                if (this.$refs.dataForm) {
                    this.$refs.dataForm.resetFields();
                }
            });
        },
        // 录入
        handleEnter() {
            this.$refs.createRef.open();
        }
    }
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: center;
    padding: 10px;
}
.create-btn {
    width: 200px !important;
    height: 40px;
}
</style>
